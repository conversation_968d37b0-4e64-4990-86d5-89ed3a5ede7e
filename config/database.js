const { Sequelize, DataTypes } = require('sequelize');
require('dotenv').config();

const getDatabaseConnection = async (division) => {
    const sequelize = new Sequelize(process.env.DBNAME, process.env.DBUSER, process.env.DBPASSWORD, {
        host: process.env.DBHOST,
        dialect: process.env.DIALECT
    });

    const LoginModel = require('../models/LoginModel')(sequelize, DataTypes, division);
    const BrandModel = require('../models/BrandModel')(sequelize, DataTypes, division);
    const MoleculeModel = require('../models/MoleculeModel')(sequelize, DataTypes, division);
    const FavouriteModel = require('../models/FavouriteModel')(sequelize, DataTypes, division);
    const UserWordModel = require('../models/UserWordModel')(sequelize, DataTypes, division);

    // Define Associations

    // Login and Favourite
    LoginModel.hasMany(FavouriteModel, { foreignKey: 'UserId' });
    FavouriteModel.belongsTo(LoginModel, { foreignKey: 'UserId' });

    // Login and UserWord
    LoginModel.hasMany(UserWordModel, { foreignKey: 'user_id' });
    UserWordModel.belongsTo(LoginModel, { foreignKey: 'user_id' });

    // UserWord and Brand (brand_id)
    BrandModel.hasMany(UserWordModel, { foreignKey: 'brand_id' });
    UserWordModel.belongsTo(BrandModel, { foreignKey: 'brand_id' });

    // UserWord and Molecule (molecule_id)
    MoleculeModel.hasMany(UserWordModel, { foreignKey: 'molecule_id' });
    UserWordModel.belongsTo(MoleculeModel, { foreignKey: 'molecule_id' });

    return {
        sequelize,
        LoginModel,
        BrandModel,
        MoleculeModel,
        FavouriteModel,
        UserWordModel
    };
};

module.exports = { getDatabaseConnection };
