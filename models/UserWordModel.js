module.exports = (sequelize, DataTypes, division) => {
    return sequelize.define(`${division}_process`, {
        word: DataTypes.STRING,
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: `${division}_organogram`,
                key: 'id'
            }
        },
        type: DataTypes.STRING,
        brand_id: {
            type: DataTypes.INTEGER,
            allowNull: true, // ✅ Important
            references: {
                model: `${division}_brands`,
                key: 'id'
            }
        },
        molecule_id: {
            type: DataTypes.INTEGER,
            allowNull: true, // ✅ Important
            references: {
                model: `${division}_molecules`,
                key: 'id'
            }
        }
    }, {
        tableName: `${division}_process`,
        timestamps: false
    });
};
