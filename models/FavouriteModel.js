module.exports = (sequelize, DataTypes, division) => {
    return sequelize.define(`${division}_favourites`, {
        UserId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: `${division}_organogram`, // actual table name of LoginModel
                key: 'id'
            }
        },
        Type: {
            type: DataTypes.ENUM('Brand', 'Molecule'),
            allowNull: false,
            validate: {
                isIn: [['Brand', 'Molecule']]
            }
        },
        Name: DataTypes.STRING,
        // Type_Name: DataTypes.STRING,
        Vocab_Breakdown: DataTypes.STRING,
        Definition: DataTypes.STRING,
        Mode_of_Action: DataTypes.STRING,
        Common_Usage: DataTypes.STRING,
        Brand_Name: {
            type: DataTypes.STRING,
            allowNull: true,
            defaultValue: null
        },
        Molecule_Name: {
            type: DataTypes.STRING,
            allowNull: true,
            defaultValue: null
        },
        Associated_Brands: {
            type: DataTypes.STRING,
            allowNull: true,
            defaultValue: null
        }
    }, {
        tableName: `${division}_favourites`,
        timestamps: true
    });
};