module.exports = (sequelize, DataTypes, division) => {
    return sequelize.define(`${division}_organogram`, {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        empCode: {
            type: DataTypes.STRING,
            field: 'EMP CODE'
        },
        empName: {
            type: DataTypes.STRING,
            field: 'EMP Name'
        },
        hq: {
            type: DataTypes.STRING,
            field: 'H.Q.'
        },
        level: {
            type: DataTypes.STRING,
            field: 'LEVEL'
        },
        region: {
            type: DataTypes.STRING,
            field: 'REGION'
        },
        status: {
            type: DataTypes.STRING,
            field: 'STATUS'
        },
        statusChangeDate: {
            type: DataTypes.DATE,
            field: 'Status Change Date'
        },
        divName: {
            type: DataTypes.STRING,
            field: 'DIV NAME'
        },
        sap: {
            type: DataTypes.STRING,
            field: 'SAP'
        },
        mobileNo: {
            type: DataTypes.STRING,
            field: 'MOBILENO.'
        },
        emailId: {
            type: DataTypes.STRING,
            field: 'EMAIL ID'
        },
        doj: {
            type: DataTypes.DATE,
            field: 'DOJ'
        },
        lastEmp: {
            type: DataTypes.STRING,
            field: 'LAST EMP'
        },
        resignDate: {
            type: DataTypes.DATE,
            field: 'RESIGN DATE'
        },
        promotionRemark: {
            type: DataTypes.STRING,
            field: 'PROMOTION REMARK'
        },
        promotionDate: {
            type: DataTypes.DATE,
            field: 'PROMOTION DATE'
        },
        transferRemark: {
            type: DataTypes.STRING,
            field: 'TRANSFER REMARK'
        },
        transferDate: {
            type: DataTypes.DATE,
            field: 'TRANSFER DATE'
        },
        dateOfExpansion: {
            type: DataTypes.DATE,
            field: 'DATE OF EXPANSION'
        },
        state: {
            type: DataTypes.STRING,
            field: 'STATE'
        },
        remarks: {
            type: DataTypes.STRING,
            field: 'REMARKS'
        },
        dateOfBirth: {
            type: DataTypes.DATE,
            field: 'Date of Birth'
        }
    }, {
        tableName: `${division}_organogram`,
        timestamps: false
    });
};
