// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.v2;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.V2";
option go_package = "cloud.google.com/go/dialogflow/apiv2/dialogflowpb;dialogflowpb";
option java_multiple_files = true;
option java_outer_classname = "GcsProto";
option java_package = "com.google.cloud.dialogflow.v2";
option objc_class_prefix = "DF";

// Google Cloud Storage location for the inputs.
message GcsSources {
  // Required. Google Cloud Storage URIs for the inputs. A URI is of the form:
  // `gs://bucket/object-prefix-or-name`
  // Whether a prefix or name is used depends on the use case.
  repeated string uris = 2 [(google.api.field_behavior) = REQUIRED];
}

// Google Cloud Storage location for the output.
message GcsDestination {
  // The Google Cloud Storage URIs for the output. A URI is of the
  // form:
  // `gs://bucket/object-prefix-or-name`
  // Whether a prefix or name is used depends on the use case. The requesting
  // user must have "write-permission" to the bucket.
  string uri = 1;
}
