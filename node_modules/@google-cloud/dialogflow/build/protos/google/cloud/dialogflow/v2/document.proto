// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dialogflow/v2/gcs.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Dialogflow.V2";
option go_package = "cloud.google.com/go/dialogflow/apiv2/dialogflowpb;dialogflowpb";
option java_multiple_files = true;
option java_outer_classname = "DocumentProto";
option java_package = "com.google.cloud.dialogflow.v2";
option objc_class_prefix = "DF";

// Service for managing knowledge
// [Documents][google.cloud.dialogflow.v2.Document].
service Documents {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Returns the list of all documents of the knowledge base.
  rpc ListDocuments(ListDocumentsRequest) returns (ListDocumentsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/knowledgeBases/*}/documents"
      additional_bindings {
        get: "/v2/{parent=projects/*/locations/*/knowledgeBases/*}/documents"
      }
      additional_bindings {
        get: "/v2/{parent=projects/*/agent/knowledgeBases/*}/documents"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves the specified document.
  rpc GetDocument(GetDocumentRequest) returns (Document) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/knowledgeBases/*/documents/*}"
      additional_bindings {
        get: "/v2/{name=projects/*/locations/*/knowledgeBases/*/documents/*}"
      }
      additional_bindings {
        get: "/v2/{name=projects/*/agent/knowledgeBases/*/documents/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new document.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`:
  // [KnowledgeOperationMetadata][google.cloud.dialogflow.v2.KnowledgeOperationMetadata]
  // - `response`: [Document][google.cloud.dialogflow.v2.Document]
  rpc CreateDocument(CreateDocumentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/knowledgeBases/*}/documents"
      body: "document"
      additional_bindings {
        post: "/v2/{parent=projects/*/locations/*/knowledgeBases/*}/documents"
        body: "document"
      }
      additional_bindings {
        post: "/v2/{parent=projects/*/agent/knowledgeBases/*}/documents"
        body: "document"
      }
    };
    option (google.api.method_signature) = "parent,document";
    option (google.longrunning.operation_info) = {
      response_type: "Document"
      metadata_type: "KnowledgeOperationMetadata"
    };
  }

  // Creates documents by importing data from external sources.
  // Dialogflow supports up to 350 documents in each request. If you try to
  // import more, Dialogflow will return an error.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`:
  // [KnowledgeOperationMetadata][google.cloud.dialogflow.v2.KnowledgeOperationMetadata]
  // - `response`:
  // [ImportDocumentsResponse][google.cloud.dialogflow.v2.ImportDocumentsResponse]
  rpc ImportDocuments(ImportDocumentsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/knowledgeBases/*}/documents:import"
      body: "*"
      additional_bindings {
        post: "/v2/{parent=projects/*/locations/*/knowledgeBases/*}/documents:import"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "ImportDocumentsResponse"
      metadata_type: "KnowledgeOperationMetadata"
    };
  }

  // Deletes the specified document.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`:
  // [KnowledgeOperationMetadata][google.cloud.dialogflow.v2.KnowledgeOperationMetadata]
  // - `response`: An [Empty
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty)
  rpc DeleteDocument(DeleteDocumentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/knowledgeBases/*/documents/*}"
      additional_bindings {
        delete: "/v2/{name=projects/*/locations/*/knowledgeBases/*/documents/*}"
      }
      additional_bindings {
        delete: "/v2/{name=projects/*/agent/knowledgeBases/*/documents/*}"
      }
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "KnowledgeOperationMetadata"
    };
  }

  // Updates the specified document.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`:
  // [KnowledgeOperationMetadata][google.cloud.dialogflow.v2.KnowledgeOperationMetadata]
  // - `response`: [Document][google.cloud.dialogflow.v2.Document]
  rpc UpdateDocument(UpdateDocumentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v2/{document.name=projects/*/knowledgeBases/*/documents/*}"
      body: "document"
      additional_bindings {
        patch: "/v2/{document.name=projects/*/locations/*/knowledgeBases/*/documents/*}"
        body: "document"
      }
      additional_bindings {
        patch: "/v2/{document.name=projects/*/agent/knowledgeBases/*/documents/*}"
        body: "document"
      }
    };
    option (google.api.method_signature) = "document,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Document"
      metadata_type: "KnowledgeOperationMetadata"
    };
  }

  // Reloads the specified document from its specified source, content_uri or
  // content. The previously loaded content of the document will be deleted.
  // Note: Even when the content of the document has not changed, there still
  // may be side effects because of internal implementation changes.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`:
  // [KnowledgeOperationMetadata][google.cloud.dialogflow.v2.KnowledgeOperationMetadata]
  // - `response`: [Document][google.cloud.dialogflow.v2.Document]
  //
  // Note: The `projects.agent.knowledgeBases.documents` resource is deprecated;
  // only use `projects.knowledgeBases.documents`.
  rpc ReloadDocument(ReloadDocumentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/knowledgeBases/*/documents/*}:reload"
      body: "*"
      additional_bindings {
        post: "/v2/{name=projects/*/locations/*/knowledgeBases/*/documents/*}:reload"
        body: "*"
      }
      additional_bindings {
        post: "/v2/{name=projects/*/agent/knowledgeBases/*/documents/*}:reload"
        body: "*"
      }
    };
    option (google.api.method_signature) = "name,content_uri";
    option (google.longrunning.operation_info) = {
      response_type: "Document"
      metadata_type: "KnowledgeOperationMetadata"
    };
  }

  // Exports a smart messaging candidate document into the specified
  // destination.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`:
  // [KnowledgeOperationMetadata][google.cloud.dialogflow.v2.KnowledgeOperationMetadata]
  // - `response`: [Document][google.cloud.dialogflow.v2.Document]
  rpc ExportDocument(ExportDocumentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/knowledgeBases/*/documents/*}:export"
      body: "*"
      additional_bindings {
        post: "/v2/{name=projects/*/locations/*/knowledgeBases/*/documents/*}:export"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "Document"
      metadata_type: "KnowledgeOperationMetadata"
    };
  }
}

// A knowledge document to be used by a
// [KnowledgeBase][google.cloud.dialogflow.v2.KnowledgeBase].
//
// For more information, see the [knowledge base
// guide](https://cloud.google.com/dialogflow/docs/how/knowledge-bases).
//
// Note: The `projects.agent.knowledgeBases.documents` resource is deprecated;
// only use `projects.knowledgeBases.documents`.
message Document {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/Document"
    pattern: "projects/{project}/knowledgeBases/{knowledge_base}/documents/{document}"
    pattern: "projects/{project}/locations/{location}/knowledgeBases/{knowledge_base}/documents/{document}"
  };

  // The knowledge type of document content.
  enum KnowledgeType {
    // The type is unspecified or arbitrary.
    KNOWLEDGE_TYPE_UNSPECIFIED = 0;

    // The document content contains question and answer pairs as either HTML or
    // CSV. Typical FAQ HTML formats are parsed accurately, but unusual formats
    // may fail to be parsed.
    //
    // CSV must have questions in the first column and answers in the second,
    // with no header. Because of this explicit format, they are always parsed
    // accurately.
    FAQ = 1;

    // Documents for which unstructured text is extracted and used for
    // question answering.
    EXTRACTIVE_QA = 2;

    // The entire document content as a whole can be used for query results.
    // Only for Contact Center Solutions on Dialogflow.
    ARTICLE_SUGGESTION = 3;

    // The document contains agent-facing Smart Reply entries.
    AGENT_FACING_SMART_REPLY = 4;
  }

  // The status of a reload attempt.
  message ReloadStatus {
    // The time of a reload attempt.
    // This reload may have been triggered automatically or manually and may
    // not have succeeded.
    google.protobuf.Timestamp time = 1;

    // The status of a reload attempt or the initial load.
    google.rpc.Status status = 2;
  }

  // Possible states of the document
  enum State {
    // The document state is unspecified.
    STATE_UNSPECIFIED = 0;

    // The document creation is in progress.
    CREATING = 1;

    // The document is active and ready to use.
    ACTIVE = 2;

    // The document updation is in progress.
    UPDATING = 3;

    // The document is reloading.
    RELOADING = 4;

    // The document deletion is in progress.
    DELETING = 5;
  }

  // Optional. The document resource name.
  // The name must be empty when creating a document.
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/knowledgeBases/<Knowledge Base ID>/documents/<Document ID>`.
  string name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. The display name of the document. The name must be 1024 bytes or
  // less; otherwise, the creation request fails.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The MIME type of this document.
  string mime_type = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The knowledge type of document content.
  repeated KnowledgeType knowledge_types = 4
      [(google.api.field_behavior) = REQUIRED];

  // Required. The source of this document.
  oneof source {
    // The URI where the file content is located.
    //
    // For documents stored in Google Cloud Storage, these URIs must have
    // the form `gs://<bucket-name>/<object-name>`.
    //
    // NOTE: External URLs must correspond to public webpages, i.e., they must
    // be indexed by Google Search. In particular, URLs for showing documents in
    // Google Cloud Storage (i.e. the URL in your browser) are not supported.
    // Instead use the `gs://` format URI described above.
    string content_uri = 5;

    // The raw content of the document. This field is only permitted for
    // EXTRACTIVE_QA and FAQ knowledge types.
    bytes raw_content = 9;
  }

  // Optional. If true, we try to automatically reload the document every day
  // (at a time picked by the system). If false or unspecified, we don't try
  // to automatically reload the document.
  //
  // Currently you can only enable automatic reload for documents sourced from
  // a public url, see `source` field for the source types.
  //
  // Reload status can be tracked in `latest_reload_status`. If a reload
  // fails, we will keep the document unchanged.
  //
  // If a reload fails with internal errors, the system will try to reload the
  // document on the next day.
  // If a reload fails with non-retriable errors (e.g. PERMISSION_DENIED), the
  // system will not try to reload the document anymore. You need to manually
  // reload the document successfully by calling `ReloadDocument` and clear the
  // errors.
  bool enable_auto_reload = 11 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The time and status of the latest reload.
  // This reload may have been triggered automatically or manually
  // and may not have succeeded.
  ReloadStatus latest_reload_status = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Metadata for the document. The metadata supports arbitrary
  // key-value pairs. Suggested use cases include storing a document's title,
  // an external URL distinct from the document's content_uri, etc.
  // The max size of a `key` or a `value` of the metadata is 1024 bytes.
  map<string, string> metadata = 7 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The current state of the document.
  State state = 13 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for
// [Documents.GetDocument][google.cloud.dialogflow.v2.Documents.GetDocument].
message GetDocumentRequest {
  // Required. The name of the document to retrieve.
  // Format `projects/<Project ID>/locations/<Location
  // ID>/knowledgeBases/<Knowledge Base ID>/documents/<Document ID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Document"
    }
  ];
}

// Request message for
// [Documents.ListDocuments][google.cloud.dialogflow.v2.Documents.ListDocuments].
message ListDocumentsRequest {
  // Required. The knowledge base to list all documents for.
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/knowledgeBases/<Knowledge Base ID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Document"
    }
  ];

  // The maximum number of items to return in a single page. By
  // default 10 and at most 100.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request.
  string page_token = 3;

  // The filter expression used to filter documents returned by the list method.
  // The expression has the following syntax:
  //
  //   <field> <operator> <value> [AND <field> <operator> <value>] ...
  //
  // The following fields and operators are supported:
  //
  // * knowledge_types with has(:) operator
  // * display_name with has(:) operator
  // * state with equals(=) operator
  //
  // Examples:
  //
  // * "knowledge_types:FAQ" matches documents with FAQ knowledge type.
  // * "display_name:customer" matches documents whose display name contains
  //   "customer".
  // * "state=ACTIVE" matches documents with ACTIVE state.
  // * "knowledge_types:FAQ AND state=ACTIVE" matches all active FAQ documents.
  //
  // For more information about filtering, see
  // [API Filtering](https://aip.dev/160).
  string filter = 4;
}

// Response message for
// [Documents.ListDocuments][google.cloud.dialogflow.v2.Documents.ListDocuments].
message ListDocumentsResponse {
  // The list of documents.
  repeated Document documents = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// Request message for
// [Documents.CreateDocument][google.cloud.dialogflow.v2.Documents.CreateDocument].
message CreateDocumentRequest {
  // Required. The knowledge base to create a document for.
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/knowledgeBases/<Knowledge Base ID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Document"
    }
  ];

  // Required. The document to create.
  Document document = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [Documents.ImportDocuments][google.cloud.dialogflow.v2.Documents.ImportDocuments].
message ImportDocumentsRequest {
  // Required. The knowledge base to import documents into.
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/knowledgeBases/<Knowledge Base ID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Document"
    }
  ];

  // Required. The source to use for importing documents.
  //
  // If the source captures multiple objects, then multiple documents will be
  // created, one corresponding to each object, and all of these documents will
  // be created using the same document template.
  //
  // Dialogflow supports up to 350 documents in each request. If you try to
  // import more, Dialogflow will return an error.
  oneof source {
    // Optional. The Google Cloud Storage location for the documents.
    // The path can include a wildcard.
    //
    // These URIs may have the forms
    // `gs://<bucket-name>/<object-name>`.
    // `gs://<bucket-name>/<object-path>/*.<extension>`.
    GcsSources gcs_source = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. Document template used for importing all the documents.
  ImportDocumentTemplate document_template = 3
      [(google.api.field_behavior) = REQUIRED];

  // Whether to import custom metadata from Google Cloud Storage.
  // Only valid when the document source is Google Cloud Storage URI.
  bool import_gcs_custom_metadata = 4;
}

// The template used for importing documents.
message ImportDocumentTemplate {
  // Required. The MIME type of the document.
  string mime_type = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The knowledge type of document content.
  repeated Document.KnowledgeType knowledge_types = 2
      [(google.api.field_behavior) = REQUIRED];

  // Metadata for the document. The metadata supports arbitrary
  // key-value pairs. Suggested use cases include storing a document's title,
  // an external URL distinct from the document's content_uri, etc.
  // The max size of a `key` or a `value` of the metadata is 1024 bytes.
  map<string, string> metadata = 3;
}

// Response message for
// [Documents.ImportDocuments][google.cloud.dialogflow.v2.Documents.ImportDocuments].
message ImportDocumentsResponse {
  // Includes details about skipped documents or any other warnings.
  repeated google.rpc.Status warnings = 1;
}

// Request message for
// [Documents.DeleteDocument][google.cloud.dialogflow.v2.Documents.DeleteDocument].
message DeleteDocumentRequest {
  // Required. The name of the document to delete.
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/knowledgeBases/<Knowledge Base ID>/documents/<Document ID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Document"
    }
  ];
}

// Request message for
// [Documents.UpdateDocument][google.cloud.dialogflow.v2.Documents.UpdateDocument].
message UpdateDocumentRequest {
  // Required. The document to update.
  Document document = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Not specified means `update all`.
  // Currently, only `display_name` can be updated, an InvalidArgument will be
  // returned for attempting to update other fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [Documents.ReloadDocument][google.cloud.dialogflow.v2.Documents.ReloadDocument].
message ReloadDocumentRequest {
  // Required. The name of the document to reload.
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/knowledgeBases/<Knowledge Base ID>/documents/<Document ID>`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Document"
    }
  ];

  // The source for document reloading.
  //
  // If provided, the service will load the contents from the source
  // and update document in the knowledge base.
  //
  // Reloading from a new document source is allowed for smart messaging
  // documents only. If you want to update the source for other document types,
  // please delete the existing document and create a new one instead.
  oneof source {
    // Optional. The path of gcs source file for reloading document content. For
    // now, only gcs uri is supported.
    //
    // For documents stored in Google Cloud Storage, these URIs must have
    // the form `gs://<bucket-name>/<object-name>`.
    string content_uri = 3 [(google.api.field_behavior) = OPTIONAL];
  }

  // Optional. Whether to import custom metadata from Google Cloud Storage.
  // Only valid when the document source is Google Cloud Storage URI.
  bool import_gcs_custom_metadata = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. When enabled, the reload request is to apply partial update to
  // the smart messaging allowlist.
  bool smart_messaging_partial_update = 5
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [Documents.ExportDocument][google.cloud.dialogflow.v2.Documents.ExportDocument].
message ExportDocumentRequest {
  // Required. The name of the document to export.
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/knowledgeBases/<Knowledge Base ID>/documents/<Document ID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Document"
    }
  ];

  // Required. The destination for the export.
  oneof destination {
    // Cloud Storage file path to export the document.
    GcsDestination gcs_destination = 2;
  }

  // When enabled, export the full content of the document including empirical
  // probability.
  bool export_full_content = 3;

  // When enabled, export the smart messaging allowlist document for partial
  // update.
  bool smart_messaging_partial_update = 5;
}

// Metadata related to the Export Data Operations (e.g. ExportDocument).
message ExportOperationMetadata {
  // Cloud Storage file path of the exported data.
  GcsDestination exported_gcs_destination = 1;
}

// Metadata in google::longrunning::Operation for Knowledge operations.
message KnowledgeOperationMetadata {
  // States of the operation.
  enum State {
    // State unspecified.
    STATE_UNSPECIFIED = 0;

    // The operation has been created.
    PENDING = 1;

    // The operation is currently running.
    RUNNING = 2;

    // The operation is done, either cancelled or completed.
    DONE = 3;
  }

  // Output only. The current state of this operation.
  State state = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The name of the knowledge base interacted with during the operation.
  string knowledge_base = 3;

  // Additional metadata for the Knowledge operation.
  oneof operation_metadata {
    // Metadata for the Export Data Operation such as the destination of export.
    ExportOperationMetadata export_operation_metadata = 4;
  }
}
