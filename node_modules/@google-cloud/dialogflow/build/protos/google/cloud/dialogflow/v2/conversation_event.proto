// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.v2;

import "google/cloud/dialogflow/v2/participant.proto";
import "google/cloud/dialogflow/v2/session.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.V2";
option go_package = "cloud.google.com/go/dialogflow/apiv2/dialogflowpb;dialogflowpb";
option java_multiple_files = true;
option java_outer_classname = "ConversationEventProto";
option java_package = "com.google.cloud.dialogflow.v2";
option objc_class_prefix = "DF";

// Represents a notification sent to Pub/Sub subscribers for conversation
// lifecycle events.
message ConversationEvent {
  // Enumeration of the types of events available.
  enum Type {
    // Type not set.
    TYPE_UNSPECIFIED = 0;

    // A new conversation has been opened. This is fired when a telephone call
    // is answered, or a conversation is created via the API.
    CONVERSATION_STARTED = 1;

    // An existing conversation has closed. This is fired when a telephone call
    // is terminated, or a conversation is closed via the API.
    CONVERSATION_FINISHED = 2;

    // An existing conversation has received notification from Dialogflow that
    // human intervention is required.
    HUMAN_INTERVENTION_NEEDED = 3;

    // An existing conversation has received a new message, either from API or
    // telephony. It is configured in
    // [ConversationProfile.new_message_event_notification_config][google.cloud.dialogflow.v2.ConversationProfile.new_message_event_notification_config]
    NEW_MESSAGE = 5;

    // An existing conversation has received a new speech recognition result.
    // This is mainly for delivering intermediate transcripts. The notification
    // is configured in
    // [ConversationProfile.new_recognition_event_notification_config][].
    NEW_RECOGNITION_RESULT = 7;

    // Unrecoverable error during a telephone call.
    //
    // In general non-recoverable errors only occur if something was
    // misconfigured in the ConversationProfile corresponding to the call. After
    // a non-recoverable error, Dialogflow may stop responding.
    //
    // We don't fire this event:
    //
    // * in an API call because we can directly return the error, or,
    // * when we can recover from an error.
    UNRECOVERABLE_ERROR = 4;
  }

  // The unique identifier of the conversation this notification
  // refers to.
  // Format: `projects/<Project ID>/conversations/<Conversation ID>`.
  string conversation = 1;

  // The type of the event that this notification refers to.
  Type type = 2;

  // More detailed information about an error. Only set for type
  // UNRECOVERABLE_ERROR_IN_PHONE_CALL.
  google.rpc.Status error_status = 3;

  // Payload of conversation event.
  oneof payload {
    // Payload of NEW_MESSAGE event.
    Message new_message_payload = 4;

    // Payload of NEW_RECOGNITION_RESULT event.
    StreamingRecognitionResult new_recognition_result_payload = 5;
  }
}
