// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dialogflow/v2/gcs.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.V2";
option go_package = "cloud.google.com/go/dialogflow/apiv2/dialogflowpb;dialogflowpb";
option java_multiple_files = true;
option java_outer_classname = "ConversationDatasetProto";
option java_package = "com.google.cloud.dialogflow.v2";
option objc_class_prefix = "DF";

// Conversation datasets.
//
// Conversation datasets contain raw conversation files and their
// customizable metadata that can be used for model training.
service ConversationDatasets {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Creates a new conversation dataset.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`:
  // [CreateConversationDatasetOperationMetadata][google.cloud.dialogflow.v2.CreateConversationDatasetOperationMetadata]
  // - `response`:
  // [ConversationDataset][google.cloud.dialogflow.v2.ConversationDataset]
  rpc CreateConversationDataset(CreateConversationDatasetRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/locations/*}/conversationDatasets"
      body: "conversation_dataset"
    };
    option (google.api.method_signature) = "parent,conversation_dataset";
    option (google.longrunning.operation_info) = {
      response_type: "ConversationDataset"
      metadata_type: "CreateConversationDatasetOperationMetadata"
    };
  }

  // Retrieves the specified conversation dataset.
  rpc GetConversationDataset(GetConversationDatasetRequest)
      returns (ConversationDataset) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/conversationDatasets/*}"
      additional_bindings {
        get: "/v2/{name=projects/*/locations/*/conversationDatasets/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Returns the list of all conversation datasets in the specified
  // project and location.
  rpc ListConversationDatasets(ListConversationDatasetsRequest)
      returns (ListConversationDatasetsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*}/conversationDatasets"
      additional_bindings {
        get: "/v2/{parent=projects/*/locations/*}/conversationDatasets"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes the specified conversation dataset.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`:
  // [DeleteConversationDatasetOperationMetadata][google.cloud.dialogflow.v2.DeleteConversationDatasetOperationMetadata]
  // - `response`: An [Empty
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty)
  rpc DeleteConversationDataset(DeleteConversationDatasetRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/locations/*/conversationDatasets/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteConversationDatasetOperationMetadata"
    };
  }

  // Import data into the specified conversation dataset. Note that it
  // is not allowed to import data to a conversation dataset that
  // already has data in it.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`:
  // [ImportConversationDataOperationMetadata][google.cloud.dialogflow.v2.ImportConversationDataOperationMetadata]
  // - `response`:
  // [ImportConversationDataOperationResponse][google.cloud.dialogflow.v2.ImportConversationDataOperationResponse]
  rpc ImportConversationData(ImportConversationDataRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/conversationDatasets/*}:importConversationData"
      body: "*"
      additional_bindings {
        post: "/v2/{name=projects/*/locations/*/conversationDatasets/*}:importConversationData"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "ImportConversationDataOperationResponse"
      metadata_type: "ImportConversationDataOperationMetadata"
    };
  }
}

// Represents metadata of a conversation.
message ConversationInfo {
  // Optional. The language code of the conversation data within this dataset.
  // See https://cloud.google.com/apis/design/standard_fields for more
  // information. Supports all UTF-8 languages.
  string language_code = 1 [(google.api.field_behavior) = OPTIONAL];
}

// Represents the configuration of importing a set of conversation files in
// Google Cloud Storage.
message InputConfig {
  // Required. Where the data is from.
  oneof source {
    // The Cloud Storage URI has the form gs://<Google Cloud Storage bucket
    // name>//agent*.json. Wildcards are allowed and will be expanded into all
    // matched JSON files, which will be read as one conversation per file.
    GcsSources gcs_source = 1;
  }
}

// Represents a conversation dataset that a user imports raw data into.
// The data inside ConversationDataset can not be changed after
// ImportConversationData finishes (and calling ImportConversationData on a
// dataset that already has data is not allowed).
message ConversationDataset {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/ConversationDataset"
    pattern: "projects/{project}/locations/{location}/conversationDatasets/{conversation_dataset}"
  };

  // Output only. ConversationDataset resource name. Format:
  // `projects/<Project ID>/locations/<Location
  // ID>/conversationDatasets/<Conversation Dataset ID>`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The display name of the dataset. Maximum of 64 bytes.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The description of the dataset. Maximum of 10000 bytes.
  string description = 3 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Creation time of this dataset.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Input configurations set during conversation data import.
  InputConfig input_config = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Metadata set during conversation data import.
  ConversationInfo conversation_info = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The number of conversations this conversation dataset
  // contains.
  int64 conversation_count = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A read only boolean field reflecting Zone Isolation status of
  // the dataset.
  optional bool satisfies_pzi = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A read only boolean field reflecting Zone Separation status of
  // the dataset.
  optional bool satisfies_pzs = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request message for
// [ConversationDatasets.CreateConversationDataset][google.cloud.dialogflow.v2.ConversationDatasets.CreateConversationDataset].
message CreateConversationDatasetRequest {
  // Required. The project to create conversation dataset for. Format:
  // `projects/<Project ID>/locations/<Location ID>`
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The conversation dataset to create.
  ConversationDataset conversation_dataset = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [ConversationDatasets.GetConversationDataset][google.cloud.dialogflow.v2.ConversationDatasets.GetConversationDataset].
message GetConversationDatasetRequest {
  // Required. The conversation dataset to retrieve. Format:
  // `projects/<Project ID>/locations/<Location
  // ID>/conversationDatasets/<Conversation Dataset ID>`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/ConversationDataset"
    }
  ];
}

// The request message for
// [ConversationDatasets.ListConversationDatasets][google.cloud.dialogflow.v2.ConversationDatasets.ListConversationDatasets].
message ListConversationDatasetsRequest {
  // Required. The project and location name to list all conversation datasets
  // for. Format: `projects/<Project ID>/locations/<Location ID>`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/ConversationDataset"
    }
  ];

  // Optional. Maximum number of conversation datasets to return in a single
  // page. By default 100 and at most 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The next_page_token value returned from a previous list request.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for
// [ConversationDatasets.ListConversationDatasets][google.cloud.dialogflow.v2.ConversationDatasets.ListConversationDatasets].
message ListConversationDatasetsResponse {
  // The list of datasets to return.
  repeated ConversationDataset conversation_datasets = 1;

  // The token to use to retrieve the next page of results, or empty if there
  // are no more results in the list.
  string next_page_token = 2;
}

// The request message for
// [ConversationDatasets.DeleteConversationDataset][google.cloud.dialogflow.v2.ConversationDatasets.DeleteConversationDataset].
message DeleteConversationDatasetRequest {
  // Required. The conversation dataset to delete. Format:
  // `projects/<Project ID>/locations/<Location
  // ID>/conversationDatasets/<Conversation Dataset ID>`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/ConversationDataset"
    }
  ];
}

// The request message for
// [ConversationDatasets.ImportConversationData][google.cloud.dialogflow.v2.ConversationDatasets.ImportConversationData].
message ImportConversationDataRequest {
  // Required. Dataset resource name. Format:
  // `projects/<Project ID>/locations/<Location
  // ID>/conversationDatasets/<Conversation Dataset ID>`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/ConversationDataset"
    }
  ];

  // Required. Configuration describing where to import data from.
  InputConfig input_config = 2 [(google.api.field_behavior) = REQUIRED];
}

// Metadata for a
// [ConversationDatasets.ImportConversationData][google.cloud.dialogflow.v2.ConversationDatasets.ImportConversationData]
// operation.
message ImportConversationDataOperationMetadata {
  // The resource name of the imported conversation dataset. Format:
  // `projects/<Project ID>/locations/<Location
  // ID>/conversationDatasets/<Conversation Dataset Id>`
  string conversation_dataset = 1 [(google.api.resource_reference) = {
    type: "dialogflow.googleapis.com/ConversationDataset"
  }];

  // Partial failures are failures that don't fail the whole long running
  // operation, e.g. single files that couldn't be read.
  repeated google.rpc.Status partial_failures = 2;

  // Timestamp when import conversation data request was created. The time is
  // measured on server side.
  google.protobuf.Timestamp create_time = 3;
}

// Response used for
// [ConversationDatasets.ImportConversationData][google.cloud.dialogflow.v2.ConversationDatasets.ImportConversationData]
// long running operation.
message ImportConversationDataOperationResponse {
  // The resource name of the imported conversation dataset. Format:
  // `projects/<Project ID>/locations/<Location
  // ID>/conversationDatasets/<Conversation Dataset Id>`
  string conversation_dataset = 1 [(google.api.resource_reference) = {
    type: "dialogflow.googleapis.com/ConversationDataset"
  }];

  // Number of conversations imported successfully.
  int32 import_count = 3;
}

// Metadata for [CreateConversationDataset][].
message CreateConversationDatasetOperationMetadata {
  // The resource name of the conversation dataset that will be created. Format:
  // `projects/<Project ID>/locations/<Location
  // ID>/conversationDatasets/<Conversation Dataset Id>`
  string conversation_dataset = 1 [(google.api.resource_reference) = {
    type: "dialogflow.googleapis.com/ConversationDataset"
  }];
}

// Metadata for [DeleteConversationDataset][].
message DeleteConversationDatasetOperationMetadata {}
