// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.v2beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dialogflow/v2beta1/validation_result.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Dialogflow.V2Beta1";
option go_package = "cloud.google.com/go/dialogflow/apiv2beta1/dialogflowpb;dialogflowpb";
option java_multiple_files = true;
option java_outer_classname = "AgentProto";
option java_package = "com.google.cloud.dialogflow.v2beta1";
option objc_class_prefix = "DF";

// Service for managing [Agents][google.cloud.dialogflow.v2beta1.Agent].
service Agents {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Retrieves the specified agent.
  rpc GetAgent(GetAgentRequest) returns (Agent) {
    option (google.api.http) = {
      get: "/v2beta1/{parent=projects/*}/agent"
      additional_bindings {
        get: "/v2beta1/{parent=projects/*/locations/*}/agent"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates/updates the specified agent.
  //
  // Note: You should always train an agent prior to sending it queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/es/docs/training).
  rpc SetAgent(SetAgentRequest) returns (Agent) {
    option (google.api.http) = {
      post: "/v2beta1/{agent.parent=projects/*}/agent"
      body: "agent"
      additional_bindings {
        post: "/v2beta1/{agent.parent=projects/*/locations/*}/agent"
        body: "agent"
      }
    };
    option (google.api.method_signature) = "agent";
  }

  // Deletes the specified agent.
  rpc DeleteAgent(DeleteAgentRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2beta1/{parent=projects/*}/agent"
      additional_bindings {
        delete: "/v2beta1/{parent=projects/*/locations/*}/agent"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns the list of agents.
  // Since there is at most one conversational agent per project, this method is
  // useful primarily for listing all agents across projects the caller has
  // access to. One can achieve that with a wildcard project collection id "-".
  // Refer to [List
  // Sub-Collections](https://cloud.google.com/apis/design/design_patterns#list_sub-collections).
  rpc SearchAgents(SearchAgentsRequest) returns (SearchAgentsResponse) {
    option (google.api.http) = {
      get: "/v2beta1/{parent=projects/*}/agent:search"
      additional_bindings {
        get: "/v2beta1/{parent=projects/*/locations/*}/agent:search"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Trains the specified agent.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`: An empty [Struct
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct)
  // - `response`: An [Empty
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty)
  //
  // Note: You should always train an agent prior to sending it queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/es/docs/training).
  rpc TrainAgent(TrainAgentRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2beta1/{parent=projects/*}/agent:train"
      body: "*"
      additional_bindings {
        post: "/v2beta1/{parent=projects/*/locations/*}/agent:train"
        body: "*"
      }
    };
    option (google.api.method_signature) = "parent";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.protobuf.Struct"
    };
  }

  // Exports the specified agent to a ZIP file.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`: An empty [Struct
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct)
  // - `response`:
  // [ExportAgentResponse][google.cloud.dialogflow.v2beta1.ExportAgentResponse]
  rpc ExportAgent(ExportAgentRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2beta1/{parent=projects/*}/agent:export"
      body: "*"
      additional_bindings {
        post: "/v2beta1/{parent=projects/*/locations/*}/agent:export"
        body: "*"
      }
    };
    option (google.api.method_signature) = "parent";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.dialogflow.v2beta1.ExportAgentResponse"
      metadata_type: "google.protobuf.Struct"
    };
  }

  // Imports the specified agent from a ZIP file.
  //
  // Uploads new intents and entity types without deleting the existing ones.
  // Intents and entity types with the same name are replaced with the new
  // versions from
  // [ImportAgentRequest][google.cloud.dialogflow.v2beta1.ImportAgentRequest].
  // After the import, the imported draft agent will be trained automatically
  // (unless disabled in agent settings). However, once the import is done,
  // training may not be completed yet. Please call
  // [TrainAgent][google.cloud.dialogflow.v2beta1.Agents.TrainAgent] and wait
  // for the operation it returns in order to train explicitly.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`: An empty [Struct
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct)
  // - `response`: An [Empty
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty)
  //
  // The operation only tracks when importing is complete, not when it is done
  // training.
  //
  // Note: You should always train an agent prior to sending it queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/es/docs/training).
  rpc ImportAgent(ImportAgentRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2beta1/{parent=projects/*}/agent:import"
      body: "*"
      additional_bindings {
        post: "/v2beta1/{parent=projects/*/locations/*}/agent:import"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.protobuf.Struct"
    };
  }

  // Restores the specified agent from a ZIP file.
  //
  // Replaces the current agent version with a new one. All the intents and
  // entity types in the older version are deleted. After the restore, the
  // restored draft agent will be trained automatically (unless disabled in
  // agent settings). However, once the restore is done, training may not be
  // completed yet. Please call
  // [TrainAgent][google.cloud.dialogflow.v2beta1.Agents.TrainAgent] and wait
  // for the operation it returns in order to train explicitly.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`: An empty [Struct
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct)
  // - `response`: An [Empty
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty)
  //
  // The operation only tracks when restoring is complete, not when it is done
  // training.
  //
  // Note: You should always train an agent prior to sending it queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/es/docs/training).
  rpc RestoreAgent(RestoreAgentRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2beta1/{parent=projects/*}/agent:restore"
      body: "*"
      additional_bindings {
        post: "/v2beta1/{parent=projects/*/locations/*}/agent:restore"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.protobuf.Struct"
    };
  }

  // Gets agent validation result. Agent validation is performed during
  // training time and is updated automatically when training is completed.
  rpc GetValidationResult(GetValidationResultRequest)
      returns (ValidationResult) {
    option (google.api.http) = {
      get: "/v2beta1/{parent=projects/*}/agent/validationResult"
      additional_bindings {
        get: "/v2beta1/{parent=projects/*/locations/*}/agent/validationResult"
      }
    };
  }
}

// A Dialogflow agent is a virtual agent that handles conversations with your
// end-users. It is a natural language understanding module that understands the
// nuances of human language. Dialogflow translates end-user text or audio
// during a conversation to structured data that your apps and services can
// understand. You design and build a Dialogflow agent to handle the types of
// conversations required for your system.
//
// For more information about agents, see the
// [Agent guide](https://cloud.google.com/dialogflow/docs/agents-overview).
//
message Agent {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/Agent"
    pattern: "projects/{project}/agent"
    pattern: "projects/{project}/locations/{location}/agent"
  };

  // Match mode determines how intents are detected from user queries.
  enum MatchMode {
    // Not specified.
    MATCH_MODE_UNSPECIFIED = 0;

    // Best for agents with a small number of examples in intents and/or wide
    // use of templates syntax and composite entities.
    MATCH_MODE_HYBRID = 1;

    // Can be used for agents with a large number of examples in intents,
    // especially the ones using @sys.any or very large custom entities.
    MATCH_MODE_ML_ONLY = 2;
  }

  // API version for the agent.
  enum ApiVersion {
    // Not specified.
    API_VERSION_UNSPECIFIED = 0;

    // Legacy V1 API.
    API_VERSION_V1 = 1;

    // V2 API.
    API_VERSION_V2 = 2;

    // V2beta1 API.
    API_VERSION_V2_BETA_1 = 3;
  }

  // Represents the agent tier.
  enum Tier {
    // Not specified. This value should never be used.
    TIER_UNSPECIFIED = 0;

    // Trial Edition, previously known as Standard Edition.
    TIER_STANDARD = 1;

    // Essentials Edition, previously known as Enterprise Essential Edition.
    TIER_ENTERPRISE = 2;

    // Essentials Edition (same as TIER_ENTERPRISE), previously known as
    // Enterprise Plus Edition.
    TIER_ENTERPRISE_PLUS = 3 [deprecated = true];
  }

  // Required. The project of this agent.
  // Format: `projects/<Project ID>` or
  //         `projects/<Project ID>/locations/<Location ID>`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Required. The name of this agent.
  string display_name = 2;

  // Required. The default language of the agent as a language tag. See
  // [Language
  // Support](https://cloud.google.com/dialogflow/docs/reference/language)
  // for a list of the currently supported language codes. This field cannot be
  // set by the `Update` method.
  string default_language_code = 3;

  // Optional. The list of all languages supported by this agent (except for the
  // `default_language_code`).
  repeated string supported_language_codes = 4;

  // Required. The time zone of this agent from the
  // [time zone database](https://www.iana.org/time-zones), e.g.,
  // America/New_York, Europe/Paris.
  string time_zone = 5;

  // Optional. The description of this agent.
  // The maximum length is 500 characters. If exceeded, the request is rejected.
  string description = 6;

  // Optional. The URI of the agent's avatar.
  // Avatars are used throughout the Dialogflow console and in the self-hosted
  // [Web
  // Demo](https://cloud.google.com/dialogflow/docs/integrations/web-demo)
  // integration.
  string avatar_uri = 7;

  // Optional. Determines whether this agent should log conversation queries.
  bool enable_logging = 8;

  // Optional. Determines how intents are detected from user queries.
  MatchMode match_mode = 9 [deprecated = true];

  // Optional. To filter out false positive results and still get variety in
  // matched natural language inputs for your agent, you can tune the machine
  // learning classification threshold. If the returned score value is less than
  // the threshold value, then a fallback intent will be triggered or, if there
  // are no fallback intents defined, no intent will be triggered. The score
  // values range from 0.0 (completely uncertain) to 1.0 (completely certain).
  // If set to 0.0, the default of 0.3 is used.
  float classification_threshold = 10;

  // Optional. API version displayed in Dialogflow console. If not specified,
  // V2 API is assumed. Clients are free to query different service endpoints
  // for different API versions. However, bots connectors and webhook calls will
  // follow the specified API version.
  ApiVersion api_version = 14;

  // Optional. The agent tier. If not specified, TIER_STANDARD is assumed.
  Tier tier = 15;
}

// The request message for
// [Agents.GetAgent][google.cloud.dialogflow.v2beta1.Agents.GetAgent].
message GetAgentRequest {
  // Required. The project that the agent to fetch is associated with.
  // Format: `projects/<Project ID>` or
  //         `projects/<Project ID>/locations/<Location ID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Agent"
    }
  ];
}

// The request message for
// [Agents.SetAgent][google.cloud.dialogflow.v2beta1.Agents.SetAgent].
message SetAgentRequest {
  // Required. The agent to update.
  Agent agent = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The mask to control which fields get updated.
  google.protobuf.FieldMask update_mask = 2;
}

// The request message for
// [Agents.DeleteAgent][google.cloud.dialogflow.v2beta1.Agents.DeleteAgent].
message DeleteAgentRequest {
  // Required. The project that the agent to delete is associated with.
  // Format: `projects/<Project ID>` or
  //         `projects/<Project ID>/locations/<Location ID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Agent"
    }
  ];
}

// Contains basic configuration for a sub-agent.
message SubAgent {
  // Required. The project of this agent.
  // Format: `projects/<Project ID>` or
  //         `projects/<Project ID>/locations/<Location ID>`.
  string project = 1;

  // Optional. The unique identifier (`environment name` in dialogflow console)
  // of this sub-agent environment. Assumes draft environment if `environment`
  // is not set.
  string environment = 2;
}

// The request message for
// [Agents.SearchAgents][google.cloud.dialogflow.v2beta1.Agents.SearchAgents].
message SearchAgentsRequest {
  // Required. The project to list agents from.
  // Format: `projects/<Project ID or '-'>` or
  //         `projects/<Project ID or '-'>/locations/<Location ID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Agent"
    }
  ];

  // Optional. The maximum number of items to return in a single page. By
  // default 100 and at most 1000.
  int32 page_size = 2;

  // Optional. The next_page_token value returned from a previous list request.
  string page_token = 3;
}

// The response message for
// [Agents.SearchAgents][google.cloud.dialogflow.v2beta1.Agents.SearchAgents].
message SearchAgentsResponse {
  // The list of agents. There will be a maximum number of items returned based
  // on the page_size field in the request.
  repeated Agent agents = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// The request message for
// [Agents.TrainAgent][google.cloud.dialogflow.v2beta1.Agents.TrainAgent].
message TrainAgentRequest {
  // Required. The project that the agent to train is associated with.
  // Format: `projects/<Project ID>` or
  //         `projects/<Project ID>/locations/<Location ID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Agent"
    }
  ];
}

// The request message for
// [Agents.ExportAgent][google.cloud.dialogflow.v2beta1.Agents.ExportAgent].
message ExportAgentRequest {
  // Required. The project that the agent to export is associated with.
  // Format: `projects/<Project ID>` or
  //         `projects/<Project ID>/locations/<Location ID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Agent"
    }
  ];

  // Optional. The
  // [Google Cloud Storage](https://cloud.google.com/storage/docs/)
  // URI to export the agent to.
  // The format of this URI must be `gs://<bucket-name>/<object-name>`.
  // If left unspecified, the serialized agent is returned inline.
  //
  // Dialogflow performs a write operation for the Cloud Storage object
  // on the caller's behalf, so your request authentication must
  // have write permissions for the object. For more information, see
  // [Dialogflow access
  // control](https://cloud.google.com/dialogflow/cx/docs/concept/access-control#storage).
  string agent_uri = 2;
}

// The response message for
// [Agents.ExportAgent][google.cloud.dialogflow.v2beta1.Agents.ExportAgent].
message ExportAgentResponse {
  // The exported agent.
  oneof agent {
    // The URI to a file containing the exported agent. This field is populated
    // only if `agent_uri` is specified in `ExportAgentRequest`.
    string agent_uri = 1;

    // Zip compressed raw byte content for agent.
    bytes agent_content = 2;
  }
}

// The request message for
// [Agents.ImportAgent][google.cloud.dialogflow.v2beta1.Agents.ImportAgent].
message ImportAgentRequest {
  // Required. The project that the agent to import is associated with.
  // Format: `projects/<Project ID>` or
  //         `projects/<Project ID>/locations/<Location ID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Agent"
    }
  ];

  // Required. The agent to import.
  oneof agent {
    // The URI to a Google Cloud Storage file containing the agent to import.
    // Note: The URI must start with "gs://".
    //
    // Dialogflow performs a read operation for the Cloud Storage object
    // on the caller's behalf, so your request authentication must
    // have read permissions for the object. For more information, see
    // [Dialogflow access
    // control](https://cloud.google.com/dialogflow/cx/docs/concept/access-control#storage).
    string agent_uri = 2;

    // Zip compressed raw byte content for agent.
    bytes agent_content = 3;
  }
}

// The request message for
// [Agents.RestoreAgent][google.cloud.dialogflow.v2beta1.Agents.RestoreAgent].
message RestoreAgentRequest {
  // Required. The project that the agent to restore is associated with.
  // Format: `projects/<Project ID>` or
  //         `projects/<Project ID>/locations/<Location ID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Agent"
    }
  ];

  // Required. The agent to restore.
  oneof agent {
    // The URI to a Google Cloud Storage file containing the agent to restore.
    // Note: The URI must start with "gs://".
    //
    // Dialogflow performs a read operation for the Cloud Storage object
    // on the caller's behalf, so your request authentication must
    // have read permissions for the object. For more information, see
    // [Dialogflow access
    // control](https://cloud.google.com/dialogflow/cx/docs/concept/access-control#storage).
    string agent_uri = 2;

    // Zip compressed raw byte content for agent.
    bytes agent_content = 3;
  }
}

// The request message for
// [Agents.GetValidationResult][google.cloud.dialogflow.v2beta1.Agents.GetValidationResult].
message GetValidationResultRequest {
  // Required. The project that the agent is associated with.
  // Format: `projects/<Project ID>` or
  //         `projects/<Project ID>/locations/<Location ID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Agent"
    }
  ];

  // Optional. The language for which you want a validation result. If not
  // specified, the agent's default language is used. [Many
  // languages](https://cloud.google.com/dialogflow/docs/reference/language)
  // are supported. Note: languages must be enabled in the agent before they can
  // be used.
  string language_code = 3 [(google.api.field_behavior) = OPTIONAL];
}
