// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.v2beta1;

import "google/cloud/dialogflow/v2beta1/participant.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Dialogflow.V2Beta1";
option go_package = "cloud.google.com/go/dialogflow/apiv2beta1/dialogflowpb;dialogflowpb";
option java_multiple_files = true;
option java_outer_classname = "HumanAgentAssistantEventProto";
option java_package = "com.google.cloud.dialogflow.v2beta1";
option objc_class_prefix = "DF";

// Output only. Represents a notification sent to Pub/Sub subscribers for
// agent assistant events in a specific conversation.
message HumanAgentAssistantEvent {
  // The conversation this notification refers to.
  // Format: `projects/<Project ID>/conversations/<Conversation ID>`.
  string conversation = 1;

  // The participant that the suggestion is compiled for. And This field is used
  // to call
  // [Participants.ListSuggestions][google.cloud.dialogflow.v2beta1.Participants.ListSuggestions]
  // API. Format: `projects/<Project ID>/conversations/<Conversation
  // ID>/participants/<Participant ID>`.
  // It will not be set in legacy workflow.
  // [HumanAgentAssistantConfig.name][google.cloud.dialogflow.v2beta1.HumanAgentAssistantConfig.name]
  // for more information.
  string participant = 3;

  // The suggestion results payload that this notification refers to. It will
  // only be set when
  // [HumanAgentAssistantConfig.SuggestionConfig.group_suggestion_responses][google.cloud.dialogflow.v2beta1.HumanAgentAssistantConfig.SuggestionConfig.group_suggestion_responses]
  // sets to true.
  repeated SuggestionResult suggestion_results = 5;
}
