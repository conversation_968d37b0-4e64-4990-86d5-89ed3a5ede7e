// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.v2beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dialogflow/v2beta1/audio_config.proto";
import "google/cloud/dialogflow/v2beta1/fulfillment.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Dialogflow.V2Beta1";
option go_package = "cloud.google.com/go/dialogflow/apiv2beta1/dialogflowpb;dialogflowpb";
option java_multiple_files = true;
option java_outer_classname = "EnvironmentProto";
option java_package = "com.google.cloud.dialogflow.v2beta1";
option objc_class_prefix = "DF";

// Service for managing
// [Environments][google.cloud.dialogflow.v2beta1.Environment].
service Environments {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Returns the list of all non-draft environments of the specified agent.
  rpc ListEnvironments(ListEnvironmentsRequest)
      returns (ListEnvironmentsResponse) {
    option (google.api.http) = {
      get: "/v2beta1/{parent=projects/*/agent}/environments"
      additional_bindings {
        get: "/v2beta1/{parent=projects/*/locations/*/agent}/environments"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves the specified agent environment.
  rpc GetEnvironment(GetEnvironmentRequest) returns (Environment) {
    option (google.api.http) = {
      get: "/v2beta1/{name=projects/*/agent/environments/*}"
      additional_bindings {
        get: "/v2beta1/{name=projects/*/locations/*/agent/environments/*}"
      }
    };
  }

  // Creates an agent environment.
  rpc CreateEnvironment(CreateEnvironmentRequest) returns (Environment) {
    option (google.api.http) = {
      post: "/v2beta1/{parent=projects/*/agent}/environments"
      body: "environment"
      additional_bindings {
        post: "/v2beta1/{parent=projects/*/locations/*/agent}/environments"
        body: "environment"
      }
    };
  }

  // Updates the specified agent environment.
  //
  // This method allows you to deploy new agent versions into the environment.
  // When an environment is pointed to a new agent version by setting
  // `environment.agent_version`, the environment is temporarily set to the
  // `LOADING` state. During that time, the environment keeps on serving the
  // previous version of the agent. After the new agent version is done loading,
  // the environment is set back to the `RUNNING` state.
  // You can use "-" as Environment ID in environment name to update version
  // in "draft" environment. WARNING: this will negate all recent changes to
  // draft and can't be undone. You may want to save the draft to a version
  // before calling this function.
  rpc UpdateEnvironment(UpdateEnvironmentRequest) returns (Environment) {
    option (google.api.http) = {
      patch: "/v2beta1/{environment.name=projects/*/agent/environments/*}"
      body: "environment"
      additional_bindings {
        patch: "/v2beta1/{environment.name=projects/*/locations/*/agent/environments/*}"
        body: "environment"
      }
    };
  }

  // Deletes the specified agent environment.
  rpc DeleteEnvironment(DeleteEnvironmentRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2beta1/{name=projects/*/agent/environments/*}"
      additional_bindings {
        delete: "/v2beta1/{name=projects/*/locations/*/agent/environments/*}"
      }
    };
  }

  // Gets the history of the specified environment.
  rpc GetEnvironmentHistory(GetEnvironmentHistoryRequest)
      returns (EnvironmentHistory) {
    option (google.api.http) = {
      get: "/v2beta1/{parent=projects/*/agent/environments/*}/history"
      additional_bindings {
        get: "/v2beta1/{parent=projects/*/locations/*/agent/environments/*}/history"
      }
    };
  }
}

// You can create multiple versions of your agent and publish them to separate
// environments.
//
// When you edit an agent, you are editing the draft agent. At any point, you
// can save the draft agent as an agent version, which is an immutable snapshot
// of your agent.
//
// When you save the draft agent, it is published to the default environment.
// When you create agent versions, you can publish them to custom environments.
// You can create a variety of custom environments for:
//
// - testing
// - development
// - production
// - etc.
//
// For more information, see the [versions and environments
// guide](https://cloud.google.com/dialogflow/docs/agents-versions).
message Environment {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/Environment"
    pattern: "projects/{project}/agent/environments/{environment}"
    pattern: "projects/{project}/locations/{location}/agent/environments/{environment}"
  };

  // Represents an environment state. When an environment is pointed to a new
  // agent version, the environment is temporarily set to the `LOADING` state.
  // During that time, the environment keeps on serving the previous version of
  // the agent. After the new agent version is done loading, the environment is
  // set back to the `RUNNING` state.
  enum State {
    // Not specified. This value is not used.
    STATE_UNSPECIFIED = 0;

    // Stopped.
    STOPPED = 1;

    // Loading.
    LOADING = 2;

    // Running.
    RUNNING = 3;
  }

  // Output only. The unique identifier of this agent environment.
  // Supported formats:
  // - `projects/<Project ID>/agent/environments/<Environment ID>`
  // - `projects/<Project ID>/locations/<Location
  // ID>/agent/environments/<Environment ID>`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The developer-provided description for this environment.
  // The maximum length is 500 characters. If exceeded, the request is rejected.
  string description = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The agent version loaded into this environment.
  // Supported formats:
  // - `projects/<Project ID>/agent/versions/<Version ID>`
  // - `projects/<Project ID>/locations/<Location ID>/agent/versions/<Version
  // ID>`
  string agent_version = 3 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Version"
    }
  ];

  // Output only. The state of this environment. This field is read-only, i.e.,
  // it cannot be set by create and update methods.
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last update time of this environment. This field is
  // read-only, i.e., it cannot be set by create and update methods.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Text to speech settings for this environment.
  TextToSpeechSettings text_to_speech_settings = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The fulfillment settings to use for this environment.
  Fulfillment fulfillment = 8 [(google.api.field_behavior) = OPTIONAL];
}

// Instructs the speech synthesizer on how to generate the output audio content.
message TextToSpeechSettings {
  // Optional. Indicates whether text to speech is enabled. Even when this field
  // is false, other settings in this proto are still retained.
  bool enable_text_to_speech = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. Audio encoding of the synthesized audio content.
  OutputAudioEncoding output_audio_encoding = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. The synthesis sample rate (in hertz) for this audio. If not
  // provided, then the synthesizer will use the default sample rate based on
  // the audio encoding. If this is different from the voice's natural sample
  // rate, then the synthesizer will honor this request by converting to the
  // desired sample rate (which might result in worse audio quality).
  int32 sample_rate_hertz = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Configuration of how speech should be synthesized, mapping from
  // language (https://cloud.google.com/dialogflow/docs/reference/language) to
  // SynthesizeSpeechConfig.
  map<string, SynthesizeSpeechConfig> synthesize_speech_configs = 4
      [(google.api.field_behavior) = OPTIONAL];
}

// The request message for
// [Environments.ListEnvironments][google.cloud.dialogflow.v2beta1.Environments.ListEnvironments].
message ListEnvironmentsRequest {
  // Required. The agent to list all environments from.
  // Format:
  // - `projects/<Project ID>/agent`
  // - `projects/<Project ID>/locations/<Location ID>/agent`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Environment"
    }
  ];

  // Optional. The maximum number of items to return in a single page. By
  // default 100 and at most 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The next_page_token value returned from a previous list request.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for
// [Environments.ListEnvironments][google.cloud.dialogflow.v2beta1.Environments.ListEnvironments].
message ListEnvironmentsResponse {
  // The list of agent environments. There will be a maximum number of items
  // returned based on the page_size field in the request.
  repeated Environment environments = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// The request message for
// [Environments.GetEnvironment][google.cloud.dialogflow.v2beta1.Environments.GetEnvironment].
message GetEnvironmentRequest {
  // Required. The name of the environment.
  // Supported formats:
  // - `projects/<Project ID>/agent/environments/<Environment ID>`
  // - `projects/<Project ID>/locations/<Location
  //   ID>/agent/environments/<Environment ID>`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Environment"
    }
  ];
}

// The request message for
// [Environments.CreateEnvironment][google.cloud.dialogflow.v2beta1.Environments.CreateEnvironment].
message CreateEnvironmentRequest {
  // Required. The agent to create an environment for.
  // Supported formats:
  // - `projects/<Project ID>/agent`
  // - `projects/<Project ID>/locations/<Location ID>/agent`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Environment"
    }
  ];

  // Required. The environment to create.
  Environment environment = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The unique id of the new environment.
  string environment_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [Environments.UpdateEnvironment][google.cloud.dialogflow.v2beta1.Environments.UpdateEnvironment].
message UpdateEnvironmentRequest {
  // Required. The environment to update.
  Environment environment = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The mask to control which fields get updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. This field is used to prevent accidental overwrite of the draft
  // environment, which is an operation that cannot be undone. To confirm that
  // the caller desires this overwrite, this field must be explicitly set to
  // true when updating the draft environment (environment ID = `-`).
  bool allow_load_to_draft_and_discard_changes = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// The request message for
// [Environments.DeleteEnvironment][google.cloud.dialogflow.v2beta1.Environments.DeleteEnvironment].
message DeleteEnvironmentRequest {
  // Required. The name of the environment to delete.
  // / Format:
  // - `projects/<Project ID>/agent/environments/<Environment ID>`
  // - `projects/<Project ID>/locations/<Location
  // ID>/agent/environments/<Environment ID>`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Environment"
    }
  ];
}

// The request message for
// [Environments.GetEnvironmentHistory][google.cloud.dialogflow.v2beta1.Environments.GetEnvironmentHistory].
message GetEnvironmentHistoryRequest {
  // Required. The name of the environment to retrieve history for.
  // Supported formats:
  // - `projects/<Project ID>/agent/environments/<Environment ID>`
  // - `projects/<Project ID>/locations/<Location
  //   ID>/agent/environments/<Environment ID>`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Environment"
    }
  ];

  // Optional. The maximum number of items to return in a single page. By
  // default 100 and at most 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The next_page_token value returned from a previous list request.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for
// [Environments.GetEnvironmentHistory][google.cloud.dialogflow.v2beta1.Environments.GetEnvironmentHistory].
message EnvironmentHistory {
  // Represents an environment history entry.
  message Entry {
    // The agent version loaded into this environment history entry.
    string agent_version = 1;

    // The developer-provided description for this environment history entry.
    string description = 2;

    // The creation time of this environment history entry.
    google.protobuf.Timestamp create_time = 3;
  }

  // Output only. The name of the environment this history is for.
  // Supported formats:
  // - `projects/<Project ID>/agent/environments/<Environment ID>`
  // - `projects/<Project ID>/locations/<Location
  //    ID>/agent/environments/<Environment ID>`
  string parent = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The list of agent environments. There will be a maximum number
  // of items returned based on the page_size field in the request.
  repeated Entry entries = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Token to retrieve the next page of results, or empty if there
  // are no more results in the list.
  string next_page_token = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}
