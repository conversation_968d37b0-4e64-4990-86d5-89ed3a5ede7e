// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.v2beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.V2Beta1";
option go_package = "cloud.google.com/go/dialogflow/apiv2beta1/dialogflowpb;dialogflowpb";
option java_multiple_files = true;
option java_outer_classname = "EntityTypeProto";
option java_package = "com.google.cloud.dialogflow.v2beta1";
option objc_class_prefix = "DF";

// Service for managing
// [EntityTypes][google.cloud.dialogflow.v2beta1.EntityType].
service EntityTypes {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Returns the list of all entity types in the specified agent.
  rpc ListEntityTypes(ListEntityTypesRequest)
      returns (ListEntityTypesResponse) {
    option (google.api.http) = {
      get: "/v2beta1/{parent=projects/*/agent}/entityTypes"
      additional_bindings {
        get: "/v2beta1/{parent=projects/*/locations/*/agent}/entityTypes"
      }
    };
    option (google.api.method_signature) = "parent";
    option (google.api.method_signature) = "parent,language_code";
  }

  // Retrieves the specified entity type.
  rpc GetEntityType(GetEntityTypeRequest) returns (EntityType) {
    option (google.api.http) = {
      get: "/v2beta1/{name=projects/*/agent/entityTypes/*}"
      additional_bindings {
        get: "/v2beta1/{name=projects/*/locations/*/agent/entityTypes/*}"
      }
    };
    option (google.api.method_signature) = "name";
    option (google.api.method_signature) = "name,language_code";
  }

  // Creates an entity type in the specified agent.
  //
  // Note: You should always train an agent prior to sending it queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/es/docs/training).
  rpc CreateEntityType(CreateEntityTypeRequest) returns (EntityType) {
    option (google.api.http) = {
      post: "/v2beta1/{parent=projects/*/agent}/entityTypes"
      body: "entity_type"
      additional_bindings {
        post: "/v2beta1/{parent=projects/*/locations/*/agent}/entityTypes"
        body: "entity_type"
      }
    };
    option (google.api.method_signature) = "parent,entity_type";
    option (google.api.method_signature) = "parent,entity_type,language_code";
  }

  // Updates the specified entity type.
  //
  // Note: You should always train an agent prior to sending it queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/es/docs/training).
  rpc UpdateEntityType(UpdateEntityTypeRequest) returns (EntityType) {
    option (google.api.http) = {
      patch: "/v2beta1/{entity_type.name=projects/*/agent/entityTypes/*}"
      body: "entity_type"
      additional_bindings {
        patch: "/v2beta1/{entity_type.name=projects/*/locations/*/agent/entityTypes/*}"
        body: "entity_type"
      }
    };
    option (google.api.method_signature) = "entity_type";
    option (google.api.method_signature) = "entity_type,language_code";
    option (google.api.method_signature) =
        "entity_type,language_code,update_mask";
  }

  // Deletes the specified entity type.
  //
  // Note: You should always train an agent prior to sending it queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/es/docs/training).
  rpc DeleteEntityType(DeleteEntityTypeRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2beta1/{name=projects/*/agent/entityTypes/*}"
      additional_bindings {
        delete: "/v2beta1/{name=projects/*/locations/*/agent/entityTypes/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Updates/Creates multiple entity types in the specified agent.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`: An empty [Struct
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct)
  // - `response`:
  // [BatchUpdateEntityTypesResponse][google.cloud.dialogflow.v2beta1.BatchUpdateEntityTypesResponse]
  //
  // Note: You should always train an agent prior to sending it queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/es/docs/training).
  rpc BatchUpdateEntityTypes(BatchUpdateEntityTypesRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2beta1/{parent=projects/*/agent}/entityTypes:batchUpdate"
      body: "*"
      additional_bindings {
        post: "/v2beta1/{parent=projects/*/locations/*/agent}/entityTypes:batchUpdate"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.dialogflow.v2beta1.BatchUpdateEntityTypesResponse"
      metadata_type: "google.protobuf.Struct"
    };
  }

  // Deletes entity types in the specified agent.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`: An empty [Struct
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct)
  // - `response`: An [Empty
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty)
  //
  // Note: You should always train an agent prior to sending it queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/es/docs/training).
  rpc BatchDeleteEntityTypes(BatchDeleteEntityTypesRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2beta1/{parent=projects/*/agent}/entityTypes:batchDelete"
      body: "*"
      additional_bindings {
        post: "/v2beta1/{parent=projects/*/locations/*/agent}/entityTypes:batchDelete"
        body: "*"
      }
    };
    option (google.api.method_signature) = "parent,entity_type_names";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.protobuf.Struct"
    };
  }

  // Creates multiple new entities in the specified entity type.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`: An empty [Struct
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct)
  // - `response`: An [Empty
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty)
  //
  // Note: You should always train an agent prior to sending it queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/es/docs/training).
  rpc BatchCreateEntities(BatchCreateEntitiesRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2beta1/{parent=projects/*/agent/entityTypes/*}/entities:batchCreate"
      body: "*"
      additional_bindings {
        post: "/v2beta1/{parent=projects/*/locations/*/agent/entityTypes/*}/entities:batchCreate"
        body: "*"
      }
    };
    option (google.api.method_signature) = "parent,entities";
    option (google.api.method_signature) = "parent,entities,language_code";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.protobuf.Struct"
    };
  }

  // Updates or creates multiple entities in the specified entity type. This
  // method does not affect entities in the entity type that aren't explicitly
  // specified in the request.
  //
  // Note: You should always train an agent prior to sending it queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/es/docs/training).
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`: An empty [Struct
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct)
  // - `response`: An [Empty
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty)
  rpc BatchUpdateEntities(BatchUpdateEntitiesRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2beta1/{parent=projects/*/agent/entityTypes/*}/entities:batchUpdate"
      body: "*"
      additional_bindings {
        post: "/v2beta1/{parent=projects/*/locations/*/agent/entityTypes/*}/entities:batchUpdate"
        body: "*"
      }
    };
    option (google.api.method_signature) = "parent,entities";
    option (google.api.method_signature) = "parent,entities,language_code";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.protobuf.Struct"
    };
  }

  // Deletes entities in the specified entity type.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`: An empty [Struct
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct)
  // - `response`: An [Empty
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty)
  //
  // Note: You should always train an agent prior to sending it queries. See the
  // [training
  // documentation](https://cloud.google.com/dialogflow/es/docs/training).
  rpc BatchDeleteEntities(BatchDeleteEntitiesRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2beta1/{parent=projects/*/agent/entityTypes/*}/entities:batchDelete"
      body: "*"
      additional_bindings {
        post: "/v2beta1/{parent=projects/*/locations/*/agent/entityTypes/*}/entities:batchDelete"
        body: "*"
      }
    };
    option (google.api.method_signature) = "parent,entity_values";
    option (google.api.method_signature) = "parent,entity_values,language_code";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.protobuf.Struct"
    };
  }
}

// Each intent parameter has a type, called the entity type, which dictates
// exactly how data from an end-user expression is extracted.
//
// Dialogflow provides predefined system entities that can match many common
// types of data. For example, there are system entities for matching dates,
// times, colors, email addresses, and so on. You can also create your own
// custom entities for matching custom data. For example, you could define a
// vegetable entity that can match the types of vegetables available for
// purchase with a grocery store agent.
//
// For more information, see the
// [Entity guide](https://cloud.google.com/dialogflow/docs/entities-overview).
message EntityType {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/EntityType"
    pattern: "projects/{project}/agent/entityTypes/{entity_type}"
    pattern: "projects/{project}/locations/{location}/agent/entityTypes/{entity_type}"
  };

  // Represents kinds of entities.
  enum Kind {
    // Not specified. This value should be never used.
    KIND_UNSPECIFIED = 0;

    // Map entity types allow mapping of a group of synonyms to a reference
    // value.
    KIND_MAP = 1;

    // List entity types contain a set of entries that do not map to reference
    // values. However, list entity types can contain references to other entity
    // types (with or without aliases).
    KIND_LIST = 2;

    // Regexp entity types allow to specify regular expressions in entries
    // values.
    KIND_REGEXP = 3;
  }

  // Represents different entity type expansion modes. Automated expansion
  // allows an agent to recognize values that have not been explicitly listed in
  // the entity (for example, new kinds of shopping list items).
  enum AutoExpansionMode {
    // Auto expansion disabled for the entity.
    AUTO_EXPANSION_MODE_UNSPECIFIED = 0;

    // Allows an agent to recognize values that have not been explicitly
    // listed in the entity.
    AUTO_EXPANSION_MODE_DEFAULT = 1;
  }

  // An **entity entry** for an associated entity type.
  message Entity {
    // Required. The primary value associated with this entity entry.
    // For example, if the entity type is *vegetable*, the value could be
    // *scallions*.
    //
    // For `KIND_MAP` entity types:
    //
    // *   A reference value to be used in place of synonyms.
    //
    // For `KIND_LIST` entity types:
    //
    // *   A string that can contain references to other entity types (with or
    //     without aliases).
    string value = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. A collection of value synonyms. For example, if the entity type
    // is *vegetable*, and `value` is *scallions*, a synonym could be *green
    // onions*.
    //
    // For `KIND_LIST` entity types:
    //
    // *   This collection must contain exactly one synonym equal to `value`.
    repeated string synonyms = 2;
  }

  // The unique identifier of the entity type.
  // Required for
  // [EntityTypes.UpdateEntityType][google.cloud.dialogflow.v2beta1.EntityTypes.UpdateEntityType]
  // and
  // [EntityTypes.BatchUpdateEntityTypes][google.cloud.dialogflow.v2beta1.EntityTypes.BatchUpdateEntityTypes]
  // methods. Supported formats:
  // - `projects/<Project ID>/agent/entityTypes/<Entity Type ID>`
  // - `projects/<Project ID>/locations/<Location ID>/agent/entityTypes/<Entity
  //   Type ID>`
  string name = 1;

  // Required. The name of the entity type.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Indicates the kind of entity type.
  Kind kind = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. Indicates whether the entity type can be automatically
  // expanded.
  AutoExpansionMode auto_expansion_mode = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The collection of entity entries associated with the entity type.
  repeated Entity entities = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Enables fuzzy entity extraction during classification.
  bool enable_fuzzy_extraction = 7 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for
// [EntityTypes.ListEntityTypes][google.cloud.dialogflow.v2beta1.EntityTypes.ListEntityTypes].
message ListEntityTypesRequest {
  // Required. The agent to list all entity types from.
  // Supported formats:
  // - `projects/<Project ID>/agent`
  // - `projects/<Project ID>/locations/<Location ID>/agent`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/EntityType"
    }
  ];

  // Optional. The language used to access language-specific data.
  // If not specified, the agent's default language is used.
  // For more information, see
  // [Multilingual intent and entity
  // data](https://cloud.google.com/dialogflow/docs/agents-multilingual#intent-entity).
  string language_code = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of items to return in a single page. By
  // default 100 and at most 1000.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The next_page_token value returned from a previous list request.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for
// [EntityTypes.ListEntityTypes][google.cloud.dialogflow.v2beta1.EntityTypes.ListEntityTypes].
message ListEntityTypesResponse {
  // The list of agent entity types. There will be a maximum number of items
  // returned based on the page_size field in the request.
  repeated EntityType entity_types = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// The request message for
// [EntityTypes.GetEntityType][google.cloud.dialogflow.v2beta1.EntityTypes.GetEntityType].
message GetEntityTypeRequest {
  // Required. The name of the entity type.
  // Supported formats:
  // - `projects/<Project ID>/agent/entityTypes/<Entity Type ID>`
  // - `projects/<Project ID>/locations/<Location ID>/agent/entityTypes/<Entity
  //   Type ID>`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/EntityType"
    }
  ];

  // Optional. The language used to access language-specific data.
  // If not specified, the agent's default language is used.
  // For more information, see
  // [Multilingual intent and entity
  // data](https://cloud.google.com/dialogflow/docs/agents-multilingual#intent-entity).
  string language_code = 2 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for
// [EntityTypes.CreateEntityType][google.cloud.dialogflow.v2beta1.EntityTypes.CreateEntityType].
message CreateEntityTypeRequest {
  // Required. The agent to create a entity type for.
  // Supported formats:
  // - `projects/<Project ID>/agent`
  // - `projects/<Project ID>/locations/<Location ID>/agent`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/EntityType"
    }
  ];

  // Required. The entity type to create.
  EntityType entity_type = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The language used to access language-specific data.
  // If not specified, the agent's default language is used.
  // For more information, see
  // [Multilingual intent and entity
  // data](https://cloud.google.com/dialogflow/docs/agents-multilingual#intent-entity).
  string language_code = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for
// [EntityTypes.UpdateEntityType][google.cloud.dialogflow.v2beta1.EntityTypes.UpdateEntityType].
message UpdateEntityTypeRequest {
  // Required. The entity type to update.
  EntityType entity_type = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The language used to access language-specific data.
  // If not specified, the agent's default language is used.
  // For more information, see
  // [Multilingual intent and entity
  // data](https://cloud.google.com/dialogflow/docs/agents-multilingual#intent-entity).
  string language_code = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The mask to control which fields get updated.
  google.protobuf.FieldMask update_mask = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// The request message for
// [EntityTypes.DeleteEntityType][google.cloud.dialogflow.v2beta1.EntityTypes.DeleteEntityType].
message DeleteEntityTypeRequest {
  // Required. The name of the entity type to delete.
  // Supported formats:
  // - `projects/<Project ID>/agent/entityTypes/<Entity Type ID>`
  // - `projects/<Project ID>/locations/<Location ID>/agent/entityTypes/<Entity
  //   Type ID>`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/EntityType"
    }
  ];
}

// The request message for
// [EntityTypes.BatchUpdateEntityTypes][google.cloud.dialogflow.v2beta1.EntityTypes.BatchUpdateEntityTypes].
message BatchUpdateEntityTypesRequest {
  // Required. The name of the agent to update or create entity types in.
  // Supported formats:
  // - `projects/<Project ID>/agent`
  // - `projects/<Project ID>/locations/<Location ID>/agent`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/EntityType"
    }
  ];

  // The source of the entity type batch.
  //
  // For each entity type in the batch:
  //
  // *   If `name` is specified, we update an existing entity type.
  // *   If `name` is not specified, we create a new entity type.
  oneof entity_type_batch {
    // The URI to a Google Cloud Storage file containing entity types to update
    // or create. The file format can either be a serialized proto (of
    // EntityBatch type) or a JSON object. Note: The URI must start with
    // "gs://".
    string entity_type_batch_uri = 2;

    // The collection of entity types to update or create.
    EntityTypeBatch entity_type_batch_inline = 3;
  }

  // Optional. The language used to access language-specific data.
  // If not specified, the agent's default language is used.
  // For more information, see
  // [Multilingual intent and entity
  // data](https://cloud.google.com/dialogflow/docs/agents-multilingual#intent-entity).
  string language_code = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The mask to control which fields get updated.
  google.protobuf.FieldMask update_mask = 5
      [(google.api.field_behavior) = OPTIONAL];
}

// The response message for
// [EntityTypes.BatchUpdateEntityTypes][google.cloud.dialogflow.v2beta1.EntityTypes.BatchUpdateEntityTypes].
message BatchUpdateEntityTypesResponse {
  // The collection of updated or created entity types.
  repeated EntityType entity_types = 1;
}

// The request message for
// [EntityTypes.BatchDeleteEntityTypes][google.cloud.dialogflow.v2beta1.EntityTypes.BatchDeleteEntityTypes].
message BatchDeleteEntityTypesRequest {
  // Required. The name of the agent to delete all entities types for.
  // Supported formats:
  // - `projects/<Project ID>/agent`,
  // - `projects/<Project ID>/locations/<Location ID>/agent`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/EntityType"
    }
  ];

  // Required. The names entity types to delete. All names must point to the
  // same agent as `parent`.
  repeated string entity_type_names = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [EntityTypes.BatchCreateEntities][google.cloud.dialogflow.v2beta1.EntityTypes.BatchCreateEntities].
message BatchCreateEntitiesRequest {
  // Required. The name of the entity type to create entities in.
  // Supported formats:
  // - `projects/<Project ID>/agent/entityTypes/<Entity Type ID>`
  // - `projects/<Project ID>/locations/<Location ID>/agent/entityTypes/<Entity
  //   Type ID>`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/EntityType"
    }
  ];

  // Required. The entities to create.
  repeated EntityType.Entity entities = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. The language used to access language-specific data.
  // If not specified, the agent's default language is used.
  // For more information, see
  // [Multilingual intent and entity
  // data](https://cloud.google.com/dialogflow/docs/agents-multilingual#intent-entity).
  string language_code = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for
// [EntityTypes.BatchUpdateEntities][google.cloud.dialogflow.v2beta1.EntityTypes.BatchUpdateEntities].
message BatchUpdateEntitiesRequest {
  // Required. The name of the entity type to update or create entities in.
  // Supported formats:
  // - `projects/<Project ID>/agent/entityTypes/<Entity Type ID>`
  // - `projects/<Project ID>/locations/<Location ID>/agent/entityTypes/<Entity
  //   Type ID>`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/EntityType"
    }
  ];

  // Required. The entities to update or create.
  repeated EntityType.Entity entities = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. The language used to access language-specific data.
  // If not specified, the agent's default language is used.
  // For more information, see
  // [Multilingual intent and entity
  // data](https://cloud.google.com/dialogflow/docs/agents-multilingual#intent-entity).
  string language_code = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The mask to control which fields get updated.
  google.protobuf.FieldMask update_mask = 4;
}

// The request message for
// [EntityTypes.BatchDeleteEntities][google.cloud.dialogflow.v2beta1.EntityTypes.BatchDeleteEntities].
message BatchDeleteEntitiesRequest {
  // Required. The name of the entity type to delete entries for.
  // Supported formats:
  // - `projects/<Project ID>/agent/entityTypes/<Entity Type ID>`
  // - `projects/<Project ID>/locations/<Location ID>/agent/entityTypes/<Entity
  //   Type ID>`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/EntityType"
    }
  ];

  // Required. The reference `values` of the entities to delete. Note that
  // these are not fully-qualified names, i.e. they don't start with
  // `projects/<Project ID>`.
  repeated string entity_values = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The language used to access language-specific data.
  // If not specified, the agent's default language is used.
  // For more information, see
  // [Multilingual intent and entity
  // data](https://cloud.google.com/dialogflow/docs/agents-multilingual#intent-entity).
  string language_code = 3 [(google.api.field_behavior) = OPTIONAL];
}

// This message is a wrapper around a collection of entity types.
message EntityTypeBatch {
  // A collection of entity types.
  repeated EntityType entity_types = 1;
}
