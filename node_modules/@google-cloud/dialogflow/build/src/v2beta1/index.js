"use strict";
// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// ** This file is automatically generated by gapic-generator-typescript. **
// ** https://github.com/googleapis/gapic-generator-typescript **
// ** All changes to this file may be overwritten. **
Object.defineProperty(exports, "__esModule", { value: true });
exports.VersionsClient = exports.SipTrunksClient = exports.SessionsClient = exports.SessionEntityTypesClient = exports.ParticipantsClient = exports.KnowledgeBasesClient = exports.IntentsClient = exports.GeneratorsClient = exports.FulfillmentsClient = exports.EnvironmentsClient = exports.EntityTypesClient = exports.EncryptionSpecServiceClient = exports.DocumentsClient = exports.ConversationsClient = exports.ConversationProfilesClient = exports.ContextsClient = exports.AnswerRecordsClient = exports.AgentsClient = void 0;
var agents_client_1 = require("./agents_client");
Object.defineProperty(exports, "AgentsClient", { enumerable: true, get: function () { return agents_client_1.AgentsClient; } });
var answer_records_client_1 = require("./answer_records_client");
Object.defineProperty(exports, "AnswerRecordsClient", { enumerable: true, get: function () { return answer_records_client_1.AnswerRecordsClient; } });
var contexts_client_1 = require("./contexts_client");
Object.defineProperty(exports, "ContextsClient", { enumerable: true, get: function () { return contexts_client_1.ContextsClient; } });
var conversation_profiles_client_1 = require("./conversation_profiles_client");
Object.defineProperty(exports, "ConversationProfilesClient", { enumerable: true, get: function () { return conversation_profiles_client_1.ConversationProfilesClient; } });
var conversations_client_1 = require("./conversations_client");
Object.defineProperty(exports, "ConversationsClient", { enumerable: true, get: function () { return conversations_client_1.ConversationsClient; } });
var documents_client_1 = require("./documents_client");
Object.defineProperty(exports, "DocumentsClient", { enumerable: true, get: function () { return documents_client_1.DocumentsClient; } });
var encryption_spec_service_client_1 = require("./encryption_spec_service_client");
Object.defineProperty(exports, "EncryptionSpecServiceClient", { enumerable: true, get: function () { return encryption_spec_service_client_1.EncryptionSpecServiceClient; } });
var entity_types_client_1 = require("./entity_types_client");
Object.defineProperty(exports, "EntityTypesClient", { enumerable: true, get: function () { return entity_types_client_1.EntityTypesClient; } });
var environments_client_1 = require("./environments_client");
Object.defineProperty(exports, "EnvironmentsClient", { enumerable: true, get: function () { return environments_client_1.EnvironmentsClient; } });
var fulfillments_client_1 = require("./fulfillments_client");
Object.defineProperty(exports, "FulfillmentsClient", { enumerable: true, get: function () { return fulfillments_client_1.FulfillmentsClient; } });
var generators_client_1 = require("./generators_client");
Object.defineProperty(exports, "GeneratorsClient", { enumerable: true, get: function () { return generators_client_1.GeneratorsClient; } });
var intents_client_1 = require("./intents_client");
Object.defineProperty(exports, "IntentsClient", { enumerable: true, get: function () { return intents_client_1.IntentsClient; } });
var knowledge_bases_client_1 = require("./knowledge_bases_client");
Object.defineProperty(exports, "KnowledgeBasesClient", { enumerable: true, get: function () { return knowledge_bases_client_1.KnowledgeBasesClient; } });
var participants_client_1 = require("./participants_client");
Object.defineProperty(exports, "ParticipantsClient", { enumerable: true, get: function () { return participants_client_1.ParticipantsClient; } });
var session_entity_types_client_1 = require("./session_entity_types_client");
Object.defineProperty(exports, "SessionEntityTypesClient", { enumerable: true, get: function () { return session_entity_types_client_1.SessionEntityTypesClient; } });
var sessions_client_1 = require("./sessions_client");
Object.defineProperty(exports, "SessionsClient", { enumerable: true, get: function () { return sessions_client_1.SessionsClient; } });
var sip_trunks_client_1 = require("./sip_trunks_client");
Object.defineProperty(exports, "SipTrunksClient", { enumerable: true, get: function () { return sip_trunks_client_1.SipTrunksClient; } });
var versions_client_1 = require("./versions_client");
Object.defineProperty(exports, "VersionsClient", { enumerable: true, get: function () { return versions_client_1.VersionsClient; } });
//# sourceMappingURL=index.js.map