{"interfaces": {"google.cloud.dialogflow.v2beta1.SessionEntityTypes": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"], "unavailable": ["UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"ListSessionEntityTypes": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "GetSessionEntityType": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "CreateSessionEntityType": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "UpdateSessionEntityType": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "DeleteSessionEntityType": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}}}}}