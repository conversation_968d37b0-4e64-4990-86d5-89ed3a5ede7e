{"interfaces": {"google.cloud.dialogflow.v2beta1.Documents": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"], "unavailable": ["UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"ListDocuments": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "GetDocument": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "CreateDocument": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "ImportDocuments": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "DeleteDocument": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "UpdateDocument": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "ReloadDocument": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}}}}}