"use strict";
// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// ** This file is automatically generated by gapic-generator-typescript. **
// ** https://github.com/googleapis/gapic-generator-typescript **
// ** All changes to this file may be overwritten. **
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionsClient = void 0;
const stream_1 = require("stream");
const jsonProtos = require("../../protos/protos.json");
/**
 * Client JSON configuration object, loaded from
 * `src/v2/sessions_client_config.json`.
 * This file defines retry strategy and timeouts for all API methods in this library.
 */
const gapicConfig = require("./sessions_client_config.json");
const version = require('../../../package.json').version;
/**
 *  A service used for session interactions.
 *
 *  For more information, see the [API interactions
 *  guide](https://cloud.google.com/dialogflow/docs/api-overview).
 * @class
 * @memberof v2
 */
class SessionsClient {
    /**
     * Construct an instance of SessionsClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://developers.google.com/identity/protocols/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new SessionsClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts, gaxInstance) {
        var _a, _b, _c, _d, _e;
        this._terminated = false;
        this.descriptors = {
            page: {},
            stream: {},
            longrunning: {},
            batching: {},
        };
        // Ensure that options include all the required fields.
        const staticMembers = this.constructor;
        if ((opts === null || opts === void 0 ? void 0 : opts.universe_domain) &&
            (opts === null || opts === void 0 ? void 0 : opts.universeDomain) &&
            (opts === null || opts === void 0 ? void 0 : opts.universe_domain) !== (opts === null || opts === void 0 ? void 0 : opts.universeDomain)) {
            throw new Error('Please set either universe_domain or universeDomain, but not both.');
        }
        const universeDomainEnvVar = typeof process === 'object' && typeof process.env === 'object'
            ? process.env['GOOGLE_CLOUD_UNIVERSE_DOMAIN']
            : undefined;
        this._universeDomain =
            (_c = (_b = (_a = opts === null || opts === void 0 ? void 0 : opts.universeDomain) !== null && _a !== void 0 ? _a : opts === null || opts === void 0 ? void 0 : opts.universe_domain) !== null && _b !== void 0 ? _b : universeDomainEnvVar) !== null && _c !== void 0 ? _c : 'googleapis.com';
        this._servicePath = 'dialogflow.' + this._universeDomain;
        const servicePath = (opts === null || opts === void 0 ? void 0 : opts.servicePath) || (opts === null || opts === void 0 ? void 0 : opts.apiEndpoint) || this._servicePath;
        this._providedCustomServicePath = !!((opts === null || opts === void 0 ? void 0 : opts.servicePath) || (opts === null || opts === void 0 ? void 0 : opts.apiEndpoint));
        const port = (opts === null || opts === void 0 ? void 0 : opts.port) || staticMembers.port;
        const clientConfig = (_d = opts === null || opts === void 0 ? void 0 : opts.clientConfig) !== null && _d !== void 0 ? _d : {};
        const fallback = (_e = opts === null || opts === void 0 ? void 0 : opts.fallback) !== null && _e !== void 0 ? _e : (typeof window !== 'undefined' && typeof (window === null || window === void 0 ? void 0 : window.fetch) === 'function');
        opts = Object.assign({ servicePath, port, clientConfig, fallback }, opts);
        // Request numeric enum values if REST transport is used.
        opts.numericEnums = true;
        // If scopes are unset in options and we're connecting to a non-default endpoint, set scopes just in case.
        if (servicePath !== this._servicePath && !('scopes' in opts)) {
            opts['scopes'] = staticMembers.scopes;
        }
        // Load google-gax module synchronously if needed
        if (!gaxInstance) {
            gaxInstance = require('google-gax');
        }
        // Choose either gRPC or proto-over-HTTP implementation of google-gax.
        this._gaxModule = opts.fallback ? gaxInstance.fallback : gaxInstance;
        // Create a `gaxGrpc` object, with any grpc-specific options sent to the client.
        this._gaxGrpc = new this._gaxModule.GrpcClient(opts);
        // Save options to use in initialize() method.
        this._opts = opts;
        // Save the auth object to the client, for use by other methods.
        this.auth = this._gaxGrpc.auth;
        // Set useJWTAccessWithScope on the auth object.
        this.auth.useJWTAccessWithScope = true;
        // Set defaultServicePath on the auth object.
        this.auth.defaultServicePath = this._servicePath;
        // Set the default scopes in auth client if needed.
        if (servicePath === this._servicePath) {
            this.auth.defaultScopes = staticMembers.scopes;
        }
        this.locationsClient = new this._gaxModule.LocationsClient(this._gaxGrpc, opts);
        // Determine the client header string.
        const clientHeader = [`gax/${this._gaxModule.version}`, `gapic/${version}`];
        if (typeof process === 'object' && 'versions' in process) {
            clientHeader.push(`gl-node/${process.versions.node}`);
        }
        else {
            clientHeader.push(`gl-web/${this._gaxModule.version}`);
        }
        if (!opts.fallback) {
            clientHeader.push(`grpc/${this._gaxGrpc.grpcVersion}`);
        }
        else {
            clientHeader.push(`rest/${this._gaxGrpc.grpcVersion}`);
        }
        if (opts.libName && opts.libVersion) {
            clientHeader.push(`${opts.libName}/${opts.libVersion}`);
        }
        // Load the applicable protos.
        this._protos = this._gaxGrpc.loadProtoJSON(jsonProtos);
        // This API contains "path templates"; forward-slash-separated
        // identifiers to uniquely identify resources within the API.
        // Create useful helper objects for these.
        this.pathTemplates = {
            conversationDatasetPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/conversationDatasets/{conversation_dataset}'),
            encryptionSpecPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/encryptionSpec'),
            generatorPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/generators/{generator}'),
            projectAgentPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/agent'),
            projectAgentEntityTypePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/agent/entityTypes/{entity_type}'),
            projectAgentEnvironmentPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/agent/environments/{environment}'),
            projectAgentEnvironmentUserSessionPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/agent/environments/{environment}/users/{user}/sessions/{session}'),
            projectAgentEnvironmentUserSessionContextPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/agent/environments/{environment}/users/{user}/sessions/{session}/contexts/{context}'),
            projectAgentEnvironmentUserSessionEntityTypePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/agent/environments/{environment}/users/{user}/sessions/{session}/entityTypes/{entity_type}'),
            projectAgentFulfillmentPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/agent/fulfillment'),
            projectAgentIntentPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/agent/intents/{intent}'),
            projectAgentSessionPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/agent/sessions/{session}'),
            projectAgentSessionContextPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/agent/sessions/{session}/contexts/{context}'),
            projectAgentSessionEntityTypePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/agent/sessions/{session}/entityTypes/{entity_type}'),
            projectAgentVersionPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/agent/versions/{version}'),
            projectAnswerRecordPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/answerRecords/{answer_record}'),
            projectConversationPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/conversations/{conversation}'),
            projectConversationMessagePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/conversations/{conversation}/messages/{message}'),
            projectConversationModelPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/conversationModels/{conversation_model}'),
            projectConversationModelEvaluationPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/conversationModels/{conversation_model}/evaluations/{evaluation}'),
            projectConversationParticipantPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/conversations/{conversation}/participants/{participant}'),
            projectConversationProfilePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/conversationProfiles/{conversation_profile}'),
            projectKnowledgeBasePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/knowledgeBases/{knowledge_base}'),
            projectKnowledgeBaseDocumentPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/knowledgeBases/{knowledge_base}/documents/{document}'),
            projectLocationAgentPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/agent'),
            projectLocationAgentEntityTypePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/agent/entityTypes/{entity_type}'),
            projectLocationAgentEnvironmentPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/agent/environments/{environment}'),
            projectLocationAgentEnvironmentUserSessionPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/agent/environments/{environment}/users/{user}/sessions/{session}'),
            projectLocationAgentEnvironmentUserSessionContextPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/agent/environments/{environment}/users/{user}/sessions/{session}/contexts/{context}'),
            projectLocationAgentEnvironmentUserSessionEntityTypePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/agent/environments/{environment}/users/{user}/sessions/{session}/entityTypes/{entity_type}'),
            projectLocationAgentFulfillmentPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/agent/fulfillment'),
            projectLocationAgentIntentPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/agent/intents/{intent}'),
            projectLocationAgentSessionPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/agent/sessions/{session}'),
            projectLocationAgentSessionContextPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/agent/sessions/{session}/contexts/{context}'),
            projectLocationAgentSessionEntityTypePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/agent/sessions/{session}/entityTypes/{entity_type}'),
            projectLocationAgentVersionPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/agent/versions/{version}'),
            projectLocationAnswerRecordPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/answerRecords/{answer_record}'),
            projectLocationConversationPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/conversations/{conversation}'),
            projectLocationConversationMessagePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/conversations/{conversation}/messages/{message}'),
            projectLocationConversationModelPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/conversationModels/{conversation_model}'),
            projectLocationConversationModelEvaluationPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/conversationModels/{conversation_model}/evaluations/{evaluation}'),
            projectLocationConversationParticipantPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/conversations/{conversation}/participants/{participant}'),
            projectLocationConversationProfilePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/conversationProfiles/{conversation_profile}'),
            projectLocationKnowledgeBasePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/knowledgeBases/{knowledge_base}'),
            projectLocationKnowledgeBaseDocumentPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/knowledgeBases/{knowledge_base}/documents/{document}'),
        };
        // Some of the methods on this service provide streaming responses.
        // Provide descriptors for these.
        this.descriptors.stream = {
            streamingDetectIntent: new this._gaxModule.StreamDescriptor(this._gaxModule.StreamType.BIDI_STREAMING, !!opts.fallback, !!opts.gaxServerStreamingRetries),
        };
        // Put together the default options sent with requests.
        this._defaults = this._gaxGrpc.constructSettings('google.cloud.dialogflow.v2.Sessions', gapicConfig, opts.clientConfig || {}, { 'x-goog-api-client': clientHeader.join(' ') });
        // Set up a dictionary of "inner API calls"; the core implementation
        // of calling the API is handled in `google-gax`, with this code
        // merely providing the destination and request information.
        this.innerApiCalls = {};
        // Add a warn function to the client constructor so it can be easily tested.
        this.warn = this._gaxModule.warn;
    }
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize() {
        // If the client stub promise is already initialized, return immediately.
        if (this.sessionsStub) {
            return this.sessionsStub;
        }
        // Put together the "service stub" for
        // google.cloud.dialogflow.v2.Sessions.
        this.sessionsStub = this._gaxGrpc.createStub(this._opts.fallback
            ? this._protos.lookupService('google.cloud.dialogflow.v2.Sessions')
            : // eslint-disable-next-line @typescript-eslint/no-explicit-any
                this._protos.google.cloud.dialogflow.v2.Sessions, this._opts, this._providedCustomServicePath);
        // Iterate over each of the methods that the service provides
        // and create an API call method for each.
        const sessionsStubMethods = ['detectIntent', 'streamingDetectIntent'];
        for (const methodName of sessionsStubMethods) {
            const callPromise = this.sessionsStub.then(stub => (...args) => {
                if (this._terminated) {
                    if (methodName in this.descriptors.stream) {
                        const stream = new stream_1.PassThrough();
                        setImmediate(() => {
                            stream.emit('error', new this._gaxModule.GoogleError('The client has already been closed.'));
                        });
                        return stream;
                    }
                    return Promise.reject('The client has already been closed.');
                }
                const func = stub[methodName];
                return func.apply(stub, args);
            }, (err) => () => {
                throw err;
            });
            const descriptor = this.descriptors.stream[methodName] || undefined;
            const apiCall = this._gaxModule.createApiCall(callPromise, this._defaults[methodName], descriptor, this._opts.fallback);
            this.innerApiCalls[methodName] = apiCall;
        }
        return this.sessionsStub;
    }
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath() {
        if (typeof process === 'object' &&
            typeof process.emitWarning === 'function') {
            process.emitWarning('Static servicePath is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'dialogflow.googleapis.com';
    }
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint() {
        if (typeof process === 'object' &&
            typeof process.emitWarning === 'function') {
            process.emitWarning('Static apiEndpoint is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'dialogflow.googleapis.com';
    }
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint() {
        return this._servicePath;
    }
    get universeDomain() {
        return this._universeDomain;
    }
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port() {
        return 443;
    }
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes() {
        return [
            'https://www.googleapis.com/auth/cloud-platform',
            'https://www.googleapis.com/auth/dialogflow',
        ];
    }
    /**
     * Return the project ID used by this class.
     * @returns {Promise} A promise that resolves to string containing the project ID.
     */
    getProjectId(callback) {
        if (callback) {
            this.auth.getProjectId(callback);
            return;
        }
        return this.auth.getProjectId();
    }
    detectIntent(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                session: (_a = request.session) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.detectIntent(request, options, callback);
    }
    /**
     * Processes a natural language query in audio format in a streaming fashion
     * and returns structured, actionable data as a result. This method is only
     * available via the gRPC API (not REST).
     *
     * If you might use
     * [Agent Assist](https://cloud.google.com/dialogflow/docs/#aa)
     * or other CCAI products now or in the future, consider using
     * {@link protos.google.cloud.dialogflow.v2.Participants.StreamingAnalyzeContent|StreamingAnalyzeContent}
     * instead of `StreamingDetectIntent`. `StreamingAnalyzeContent` has
     * additional functionality for Agent Assist and other CCAI products.
     *
     * Note: Always use agent versions for production traffic.
     * See [Versions and
     * environments](https://cloud.google.com/dialogflow/es/docs/agents-versions).
     *
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which is both readable and writable. It accepts objects
     *   representing {@link protos.google.cloud.dialogflow.v2.StreamingDetectIntentRequest|StreamingDetectIntentRequest} for write() method, and
     *   will emit objects representing {@link protos.google.cloud.dialogflow.v2.StreamingDetectIntentResponse|StreamingDetectIntentResponse} on 'data' event asynchronously.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#bi-directional-streaming | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v2/sessions.streaming_detect_intent.js</caption>
     * region_tag:dialogflow_v2_generated_Sessions_StreamingDetectIntent_async
     */
    streamingDetectIntent(options) {
        this.initialize();
        return this.innerApiCalls.streamingDetectIntent(null, options);
    }
    /**
     * Gets information about a location.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Resource name for the location.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html | CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.cloud.location.Location | Location}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example
     * ```
     * const [response] = await client.getLocation(request);
     * ```
     */
    getLocation(request, options, callback) {
        return this.locationsClient.getLocation(request, options, callback);
    }
    /**
     * Lists information about the supported locations for this service. Returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   The resource that owns the locations collection, if applicable.
     * @param {string} request.filter
     *   The standard list filter.
     * @param {number} request.pageSize
     *   The standard list page size.
     * @param {string} request.pageToken
     *   The standard list page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link google.cloud.location.Location | Location}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example
     * ```
     * const iterable = client.listLocationsAsync(request);
     * for await (const response of iterable) {
     *   // process response
     * }
     * ```
     */
    listLocationsAsync(request, options) {
        return this.locationsClient.listLocationsAsync(request, options);
    }
    // --------------------
    // -- Path templates --
    // --------------------
    /**
     * Return a fully-qualified conversationDataset resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} conversation_dataset
     * @returns {string} Resource name string.
     */
    conversationDatasetPath(project, location, conversationDataset) {
        return this.pathTemplates.conversationDatasetPathTemplate.render({
            project: project,
            location: location,
            conversation_dataset: conversationDataset,
        });
    }
    /**
     * Parse the project from ConversationDataset resource.
     *
     * @param {string} conversationDatasetName
     *   A fully-qualified path representing ConversationDataset resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromConversationDatasetName(conversationDatasetName) {
        return this.pathTemplates.conversationDatasetPathTemplate.match(conversationDatasetName).project;
    }
    /**
     * Parse the location from ConversationDataset resource.
     *
     * @param {string} conversationDatasetName
     *   A fully-qualified path representing ConversationDataset resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromConversationDatasetName(conversationDatasetName) {
        return this.pathTemplates.conversationDatasetPathTemplate.match(conversationDatasetName).location;
    }
    /**
     * Parse the conversation_dataset from ConversationDataset resource.
     *
     * @param {string} conversationDatasetName
     *   A fully-qualified path representing ConversationDataset resource.
     * @returns {string} A string representing the conversation_dataset.
     */
    matchConversationDatasetFromConversationDatasetName(conversationDatasetName) {
        return this.pathTemplates.conversationDatasetPathTemplate.match(conversationDatasetName).conversation_dataset;
    }
    /**
     * Return a fully-qualified encryptionSpec resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @returns {string} Resource name string.
     */
    encryptionSpecPath(project, location) {
        return this.pathTemplates.encryptionSpecPathTemplate.render({
            project: project,
            location: location,
        });
    }
    /**
     * Parse the project from EncryptionSpec resource.
     *
     * @param {string} encryptionSpecName
     *   A fully-qualified path representing EncryptionSpec resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromEncryptionSpecName(encryptionSpecName) {
        return this.pathTemplates.encryptionSpecPathTemplate.match(encryptionSpecName).project;
    }
    /**
     * Parse the location from EncryptionSpec resource.
     *
     * @param {string} encryptionSpecName
     *   A fully-qualified path representing EncryptionSpec resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromEncryptionSpecName(encryptionSpecName) {
        return this.pathTemplates.encryptionSpecPathTemplate.match(encryptionSpecName).location;
    }
    /**
     * Return a fully-qualified generator resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} generator
     * @returns {string} Resource name string.
     */
    generatorPath(project, location, generator) {
        return this.pathTemplates.generatorPathTemplate.render({
            project: project,
            location: location,
            generator: generator,
        });
    }
    /**
     * Parse the project from Generator resource.
     *
     * @param {string} generatorName
     *   A fully-qualified path representing Generator resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromGeneratorName(generatorName) {
        return this.pathTemplates.generatorPathTemplate.match(generatorName)
            .project;
    }
    /**
     * Parse the location from Generator resource.
     *
     * @param {string} generatorName
     *   A fully-qualified path representing Generator resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromGeneratorName(generatorName) {
        return this.pathTemplates.generatorPathTemplate.match(generatorName)
            .location;
    }
    /**
     * Parse the generator from Generator resource.
     *
     * @param {string} generatorName
     *   A fully-qualified path representing Generator resource.
     * @returns {string} A string representing the generator.
     */
    matchGeneratorFromGeneratorName(generatorName) {
        return this.pathTemplates.generatorPathTemplate.match(generatorName)
            .generator;
    }
    /**
     * Return a fully-qualified projectAgent resource name string.
     *
     * @param {string} project
     * @returns {string} Resource name string.
     */
    projectAgentPath(project) {
        return this.pathTemplates.projectAgentPathTemplate.render({
            project: project,
        });
    }
    /**
     * Parse the project from ProjectAgent resource.
     *
     * @param {string} projectAgentName
     *   A fully-qualified path representing project_agent resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectAgentName(projectAgentName) {
        return this.pathTemplates.projectAgentPathTemplate.match(projectAgentName)
            .project;
    }
    /**
     * Return a fully-qualified projectAgentEntityType resource name string.
     *
     * @param {string} project
     * @param {string} entity_type
     * @returns {string} Resource name string.
     */
    projectAgentEntityTypePath(project, entityType) {
        return this.pathTemplates.projectAgentEntityTypePathTemplate.render({
            project: project,
            entity_type: entityType,
        });
    }
    /**
     * Parse the project from ProjectAgentEntityType resource.
     *
     * @param {string} projectAgentEntityTypeName
     *   A fully-qualified path representing project_agent_entity_type resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectAgentEntityTypeName(projectAgentEntityTypeName) {
        return this.pathTemplates.projectAgentEntityTypePathTemplate.match(projectAgentEntityTypeName).project;
    }
    /**
     * Parse the entity_type from ProjectAgentEntityType resource.
     *
     * @param {string} projectAgentEntityTypeName
     *   A fully-qualified path representing project_agent_entity_type resource.
     * @returns {string} A string representing the entity_type.
     */
    matchEntityTypeFromProjectAgentEntityTypeName(projectAgentEntityTypeName) {
        return this.pathTemplates.projectAgentEntityTypePathTemplate.match(projectAgentEntityTypeName).entity_type;
    }
    /**
     * Return a fully-qualified projectAgentEnvironment resource name string.
     *
     * @param {string} project
     * @param {string} environment
     * @returns {string} Resource name string.
     */
    projectAgentEnvironmentPath(project, environment) {
        return this.pathTemplates.projectAgentEnvironmentPathTemplate.render({
            project: project,
            environment: environment,
        });
    }
    /**
     * Parse the project from ProjectAgentEnvironment resource.
     *
     * @param {string} projectAgentEnvironmentName
     *   A fully-qualified path representing project_agent_environment resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectAgentEnvironmentName(projectAgentEnvironmentName) {
        return this.pathTemplates.projectAgentEnvironmentPathTemplate.match(projectAgentEnvironmentName).project;
    }
    /**
     * Parse the environment from ProjectAgentEnvironment resource.
     *
     * @param {string} projectAgentEnvironmentName
     *   A fully-qualified path representing project_agent_environment resource.
     * @returns {string} A string representing the environment.
     */
    matchEnvironmentFromProjectAgentEnvironmentName(projectAgentEnvironmentName) {
        return this.pathTemplates.projectAgentEnvironmentPathTemplate.match(projectAgentEnvironmentName).environment;
    }
    /**
     * Return a fully-qualified projectAgentEnvironmentUserSession resource name string.
     *
     * @param {string} project
     * @param {string} environment
     * @param {string} user
     * @param {string} session
     * @returns {string} Resource name string.
     */
    projectAgentEnvironmentUserSessionPath(project, environment, user, session) {
        return this.pathTemplates.projectAgentEnvironmentUserSessionPathTemplate.render({
            project: project,
            environment: environment,
            user: user,
            session: session,
        });
    }
    /**
     * Parse the project from ProjectAgentEnvironmentUserSession resource.
     *
     * @param {string} projectAgentEnvironmentUserSessionName
     *   A fully-qualified path representing project_agent_environment_user_session resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectAgentEnvironmentUserSessionName(projectAgentEnvironmentUserSessionName) {
        return this.pathTemplates.projectAgentEnvironmentUserSessionPathTemplate.match(projectAgentEnvironmentUserSessionName).project;
    }
    /**
     * Parse the environment from ProjectAgentEnvironmentUserSession resource.
     *
     * @param {string} projectAgentEnvironmentUserSessionName
     *   A fully-qualified path representing project_agent_environment_user_session resource.
     * @returns {string} A string representing the environment.
     */
    matchEnvironmentFromProjectAgentEnvironmentUserSessionName(projectAgentEnvironmentUserSessionName) {
        return this.pathTemplates.projectAgentEnvironmentUserSessionPathTemplate.match(projectAgentEnvironmentUserSessionName).environment;
    }
    /**
     * Parse the user from ProjectAgentEnvironmentUserSession resource.
     *
     * @param {string} projectAgentEnvironmentUserSessionName
     *   A fully-qualified path representing project_agent_environment_user_session resource.
     * @returns {string} A string representing the user.
     */
    matchUserFromProjectAgentEnvironmentUserSessionName(projectAgentEnvironmentUserSessionName) {
        return this.pathTemplates.projectAgentEnvironmentUserSessionPathTemplate.match(projectAgentEnvironmentUserSessionName).user;
    }
    /**
     * Parse the session from ProjectAgentEnvironmentUserSession resource.
     *
     * @param {string} projectAgentEnvironmentUserSessionName
     *   A fully-qualified path representing project_agent_environment_user_session resource.
     * @returns {string} A string representing the session.
     */
    matchSessionFromProjectAgentEnvironmentUserSessionName(projectAgentEnvironmentUserSessionName) {
        return this.pathTemplates.projectAgentEnvironmentUserSessionPathTemplate.match(projectAgentEnvironmentUserSessionName).session;
    }
    /**
     * Return a fully-qualified projectAgentEnvironmentUserSessionContext resource name string.
     *
     * @param {string} project
     * @param {string} environment
     * @param {string} user
     * @param {string} session
     * @param {string} context
     * @returns {string} Resource name string.
     */
    projectAgentEnvironmentUserSessionContextPath(project, environment, user, session, context) {
        return this.pathTemplates.projectAgentEnvironmentUserSessionContextPathTemplate.render({
            project: project,
            environment: environment,
            user: user,
            session: session,
            context: context,
        });
    }
    /**
     * Parse the project from ProjectAgentEnvironmentUserSessionContext resource.
     *
     * @param {string} projectAgentEnvironmentUserSessionContextName
     *   A fully-qualified path representing project_agent_environment_user_session_context resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectAgentEnvironmentUserSessionContextName(projectAgentEnvironmentUserSessionContextName) {
        return this.pathTemplates.projectAgentEnvironmentUserSessionContextPathTemplate.match(projectAgentEnvironmentUserSessionContextName).project;
    }
    /**
     * Parse the environment from ProjectAgentEnvironmentUserSessionContext resource.
     *
     * @param {string} projectAgentEnvironmentUserSessionContextName
     *   A fully-qualified path representing project_agent_environment_user_session_context resource.
     * @returns {string} A string representing the environment.
     */
    matchEnvironmentFromProjectAgentEnvironmentUserSessionContextName(projectAgentEnvironmentUserSessionContextName) {
        return this.pathTemplates.projectAgentEnvironmentUserSessionContextPathTemplate.match(projectAgentEnvironmentUserSessionContextName).environment;
    }
    /**
     * Parse the user from ProjectAgentEnvironmentUserSessionContext resource.
     *
     * @param {string} projectAgentEnvironmentUserSessionContextName
     *   A fully-qualified path representing project_agent_environment_user_session_context resource.
     * @returns {string} A string representing the user.
     */
    matchUserFromProjectAgentEnvironmentUserSessionContextName(projectAgentEnvironmentUserSessionContextName) {
        return this.pathTemplates.projectAgentEnvironmentUserSessionContextPathTemplate.match(projectAgentEnvironmentUserSessionContextName).user;
    }
    /**
     * Parse the session from ProjectAgentEnvironmentUserSessionContext resource.
     *
     * @param {string} projectAgentEnvironmentUserSessionContextName
     *   A fully-qualified path representing project_agent_environment_user_session_context resource.
     * @returns {string} A string representing the session.
     */
    matchSessionFromProjectAgentEnvironmentUserSessionContextName(projectAgentEnvironmentUserSessionContextName) {
        return this.pathTemplates.projectAgentEnvironmentUserSessionContextPathTemplate.match(projectAgentEnvironmentUserSessionContextName).session;
    }
    /**
     * Parse the context from ProjectAgentEnvironmentUserSessionContext resource.
     *
     * @param {string} projectAgentEnvironmentUserSessionContextName
     *   A fully-qualified path representing project_agent_environment_user_session_context resource.
     * @returns {string} A string representing the context.
     */
    matchContextFromProjectAgentEnvironmentUserSessionContextName(projectAgentEnvironmentUserSessionContextName) {
        return this.pathTemplates.projectAgentEnvironmentUserSessionContextPathTemplate.match(projectAgentEnvironmentUserSessionContextName).context;
    }
    /**
     * Return a fully-qualified projectAgentEnvironmentUserSessionEntityType resource name string.
     *
     * @param {string} project
     * @param {string} environment
     * @param {string} user
     * @param {string} session
     * @param {string} entity_type
     * @returns {string} Resource name string.
     */
    projectAgentEnvironmentUserSessionEntityTypePath(project, environment, user, session, entityType) {
        return this.pathTemplates.projectAgentEnvironmentUserSessionEntityTypePathTemplate.render({
            project: project,
            environment: environment,
            user: user,
            session: session,
            entity_type: entityType,
        });
    }
    /**
     * Parse the project from ProjectAgentEnvironmentUserSessionEntityType resource.
     *
     * @param {string} projectAgentEnvironmentUserSessionEntityTypeName
     *   A fully-qualified path representing project_agent_environment_user_session_entity_type resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectAgentEnvironmentUserSessionEntityTypeName(projectAgentEnvironmentUserSessionEntityTypeName) {
        return this.pathTemplates.projectAgentEnvironmentUserSessionEntityTypePathTemplate.match(projectAgentEnvironmentUserSessionEntityTypeName).project;
    }
    /**
     * Parse the environment from ProjectAgentEnvironmentUserSessionEntityType resource.
     *
     * @param {string} projectAgentEnvironmentUserSessionEntityTypeName
     *   A fully-qualified path representing project_agent_environment_user_session_entity_type resource.
     * @returns {string} A string representing the environment.
     */
    matchEnvironmentFromProjectAgentEnvironmentUserSessionEntityTypeName(projectAgentEnvironmentUserSessionEntityTypeName) {
        return this.pathTemplates.projectAgentEnvironmentUserSessionEntityTypePathTemplate.match(projectAgentEnvironmentUserSessionEntityTypeName).environment;
    }
    /**
     * Parse the user from ProjectAgentEnvironmentUserSessionEntityType resource.
     *
     * @param {string} projectAgentEnvironmentUserSessionEntityTypeName
     *   A fully-qualified path representing project_agent_environment_user_session_entity_type resource.
     * @returns {string} A string representing the user.
     */
    matchUserFromProjectAgentEnvironmentUserSessionEntityTypeName(projectAgentEnvironmentUserSessionEntityTypeName) {
        return this.pathTemplates.projectAgentEnvironmentUserSessionEntityTypePathTemplate.match(projectAgentEnvironmentUserSessionEntityTypeName).user;
    }
    /**
     * Parse the session from ProjectAgentEnvironmentUserSessionEntityType resource.
     *
     * @param {string} projectAgentEnvironmentUserSessionEntityTypeName
     *   A fully-qualified path representing project_agent_environment_user_session_entity_type resource.
     * @returns {string} A string representing the session.
     */
    matchSessionFromProjectAgentEnvironmentUserSessionEntityTypeName(projectAgentEnvironmentUserSessionEntityTypeName) {
        return this.pathTemplates.projectAgentEnvironmentUserSessionEntityTypePathTemplate.match(projectAgentEnvironmentUserSessionEntityTypeName).session;
    }
    /**
     * Parse the entity_type from ProjectAgentEnvironmentUserSessionEntityType resource.
     *
     * @param {string} projectAgentEnvironmentUserSessionEntityTypeName
     *   A fully-qualified path representing project_agent_environment_user_session_entity_type resource.
     * @returns {string} A string representing the entity_type.
     */
    matchEntityTypeFromProjectAgentEnvironmentUserSessionEntityTypeName(projectAgentEnvironmentUserSessionEntityTypeName) {
        return this.pathTemplates.projectAgentEnvironmentUserSessionEntityTypePathTemplate.match(projectAgentEnvironmentUserSessionEntityTypeName).entity_type;
    }
    /**
     * Return a fully-qualified projectAgentFulfillment resource name string.
     *
     * @param {string} project
     * @returns {string} Resource name string.
     */
    projectAgentFulfillmentPath(project) {
        return this.pathTemplates.projectAgentFulfillmentPathTemplate.render({
            project: project,
        });
    }
    /**
     * Parse the project from ProjectAgentFulfillment resource.
     *
     * @param {string} projectAgentFulfillmentName
     *   A fully-qualified path representing project_agent_fulfillment resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectAgentFulfillmentName(projectAgentFulfillmentName) {
        return this.pathTemplates.projectAgentFulfillmentPathTemplate.match(projectAgentFulfillmentName).project;
    }
    /**
     * Return a fully-qualified projectAgentIntent resource name string.
     *
     * @param {string} project
     * @param {string} intent
     * @returns {string} Resource name string.
     */
    projectAgentIntentPath(project, intent) {
        return this.pathTemplates.projectAgentIntentPathTemplate.render({
            project: project,
            intent: intent,
        });
    }
    /**
     * Parse the project from ProjectAgentIntent resource.
     *
     * @param {string} projectAgentIntentName
     *   A fully-qualified path representing project_agent_intent resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectAgentIntentName(projectAgentIntentName) {
        return this.pathTemplates.projectAgentIntentPathTemplate.match(projectAgentIntentName).project;
    }
    /**
     * Parse the intent from ProjectAgentIntent resource.
     *
     * @param {string} projectAgentIntentName
     *   A fully-qualified path representing project_agent_intent resource.
     * @returns {string} A string representing the intent.
     */
    matchIntentFromProjectAgentIntentName(projectAgentIntentName) {
        return this.pathTemplates.projectAgentIntentPathTemplate.match(projectAgentIntentName).intent;
    }
    /**
     * Return a fully-qualified projectAgentSession resource name string.
     *
     * @param {string} project
     * @param {string} session
     * @returns {string} Resource name string.
     */
    projectAgentSessionPath(project, session) {
        return this.pathTemplates.projectAgentSessionPathTemplate.render({
            project: project,
            session: session,
        });
    }
    /**
     * Parse the project from ProjectAgentSession resource.
     *
     * @param {string} projectAgentSessionName
     *   A fully-qualified path representing project_agent_session resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectAgentSessionName(projectAgentSessionName) {
        return this.pathTemplates.projectAgentSessionPathTemplate.match(projectAgentSessionName).project;
    }
    /**
     * Parse the session from ProjectAgentSession resource.
     *
     * @param {string} projectAgentSessionName
     *   A fully-qualified path representing project_agent_session resource.
     * @returns {string} A string representing the session.
     */
    matchSessionFromProjectAgentSessionName(projectAgentSessionName) {
        return this.pathTemplates.projectAgentSessionPathTemplate.match(projectAgentSessionName).session;
    }
    /**
     * Return a fully-qualified projectAgentSessionContext resource name string.
     *
     * @param {string} project
     * @param {string} session
     * @param {string} context
     * @returns {string} Resource name string.
     */
    projectAgentSessionContextPath(project, session, context) {
        return this.pathTemplates.projectAgentSessionContextPathTemplate.render({
            project: project,
            session: session,
            context: context,
        });
    }
    /**
     * Parse the project from ProjectAgentSessionContext resource.
     *
     * @param {string} projectAgentSessionContextName
     *   A fully-qualified path representing project_agent_session_context resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectAgentSessionContextName(projectAgentSessionContextName) {
        return this.pathTemplates.projectAgentSessionContextPathTemplate.match(projectAgentSessionContextName).project;
    }
    /**
     * Parse the session from ProjectAgentSessionContext resource.
     *
     * @param {string} projectAgentSessionContextName
     *   A fully-qualified path representing project_agent_session_context resource.
     * @returns {string} A string representing the session.
     */
    matchSessionFromProjectAgentSessionContextName(projectAgentSessionContextName) {
        return this.pathTemplates.projectAgentSessionContextPathTemplate.match(projectAgentSessionContextName).session;
    }
    /**
     * Parse the context from ProjectAgentSessionContext resource.
     *
     * @param {string} projectAgentSessionContextName
     *   A fully-qualified path representing project_agent_session_context resource.
     * @returns {string} A string representing the context.
     */
    matchContextFromProjectAgentSessionContextName(projectAgentSessionContextName) {
        return this.pathTemplates.projectAgentSessionContextPathTemplate.match(projectAgentSessionContextName).context;
    }
    /**
     * Return a fully-qualified projectAgentSessionEntityType resource name string.
     *
     * @param {string} project
     * @param {string} session
     * @param {string} entity_type
     * @returns {string} Resource name string.
     */
    projectAgentSessionEntityTypePath(project, session, entityType) {
        return this.pathTemplates.projectAgentSessionEntityTypePathTemplate.render({
            project: project,
            session: session,
            entity_type: entityType,
        });
    }
    /**
     * Parse the project from ProjectAgentSessionEntityType resource.
     *
     * @param {string} projectAgentSessionEntityTypeName
     *   A fully-qualified path representing project_agent_session_entity_type resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectAgentSessionEntityTypeName(projectAgentSessionEntityTypeName) {
        return this.pathTemplates.projectAgentSessionEntityTypePathTemplate.match(projectAgentSessionEntityTypeName).project;
    }
    /**
     * Parse the session from ProjectAgentSessionEntityType resource.
     *
     * @param {string} projectAgentSessionEntityTypeName
     *   A fully-qualified path representing project_agent_session_entity_type resource.
     * @returns {string} A string representing the session.
     */
    matchSessionFromProjectAgentSessionEntityTypeName(projectAgentSessionEntityTypeName) {
        return this.pathTemplates.projectAgentSessionEntityTypePathTemplate.match(projectAgentSessionEntityTypeName).session;
    }
    /**
     * Parse the entity_type from ProjectAgentSessionEntityType resource.
     *
     * @param {string} projectAgentSessionEntityTypeName
     *   A fully-qualified path representing project_agent_session_entity_type resource.
     * @returns {string} A string representing the entity_type.
     */
    matchEntityTypeFromProjectAgentSessionEntityTypeName(projectAgentSessionEntityTypeName) {
        return this.pathTemplates.projectAgentSessionEntityTypePathTemplate.match(projectAgentSessionEntityTypeName).entity_type;
    }
    /**
     * Return a fully-qualified projectAgentVersion resource name string.
     *
     * @param {string} project
     * @param {string} version
     * @returns {string} Resource name string.
     */
    projectAgentVersionPath(project, version) {
        return this.pathTemplates.projectAgentVersionPathTemplate.render({
            project: project,
            version: version,
        });
    }
    /**
     * Parse the project from ProjectAgentVersion resource.
     *
     * @param {string} projectAgentVersionName
     *   A fully-qualified path representing project_agent_version resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectAgentVersionName(projectAgentVersionName) {
        return this.pathTemplates.projectAgentVersionPathTemplate.match(projectAgentVersionName).project;
    }
    /**
     * Parse the version from ProjectAgentVersion resource.
     *
     * @param {string} projectAgentVersionName
     *   A fully-qualified path representing project_agent_version resource.
     * @returns {string} A string representing the version.
     */
    matchVersionFromProjectAgentVersionName(projectAgentVersionName) {
        return this.pathTemplates.projectAgentVersionPathTemplate.match(projectAgentVersionName).version;
    }
    /**
     * Return a fully-qualified projectAnswerRecord resource name string.
     *
     * @param {string} project
     * @param {string} answer_record
     * @returns {string} Resource name string.
     */
    projectAnswerRecordPath(project, answerRecord) {
        return this.pathTemplates.projectAnswerRecordPathTemplate.render({
            project: project,
            answer_record: answerRecord,
        });
    }
    /**
     * Parse the project from ProjectAnswerRecord resource.
     *
     * @param {string} projectAnswerRecordName
     *   A fully-qualified path representing project_answer_record resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectAnswerRecordName(projectAnswerRecordName) {
        return this.pathTemplates.projectAnswerRecordPathTemplate.match(projectAnswerRecordName).project;
    }
    /**
     * Parse the answer_record from ProjectAnswerRecord resource.
     *
     * @param {string} projectAnswerRecordName
     *   A fully-qualified path representing project_answer_record resource.
     * @returns {string} A string representing the answer_record.
     */
    matchAnswerRecordFromProjectAnswerRecordName(projectAnswerRecordName) {
        return this.pathTemplates.projectAnswerRecordPathTemplate.match(projectAnswerRecordName).answer_record;
    }
    /**
     * Return a fully-qualified projectConversation resource name string.
     *
     * @param {string} project
     * @param {string} conversation
     * @returns {string} Resource name string.
     */
    projectConversationPath(project, conversation) {
        return this.pathTemplates.projectConversationPathTemplate.render({
            project: project,
            conversation: conversation,
        });
    }
    /**
     * Parse the project from ProjectConversation resource.
     *
     * @param {string} projectConversationName
     *   A fully-qualified path representing project_conversation resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectConversationName(projectConversationName) {
        return this.pathTemplates.projectConversationPathTemplate.match(projectConversationName).project;
    }
    /**
     * Parse the conversation from ProjectConversation resource.
     *
     * @param {string} projectConversationName
     *   A fully-qualified path representing project_conversation resource.
     * @returns {string} A string representing the conversation.
     */
    matchConversationFromProjectConversationName(projectConversationName) {
        return this.pathTemplates.projectConversationPathTemplate.match(projectConversationName).conversation;
    }
    /**
     * Return a fully-qualified projectConversationMessage resource name string.
     *
     * @param {string} project
     * @param {string} conversation
     * @param {string} message
     * @returns {string} Resource name string.
     */
    projectConversationMessagePath(project, conversation, message) {
        return this.pathTemplates.projectConversationMessagePathTemplate.render({
            project: project,
            conversation: conversation,
            message: message,
        });
    }
    /**
     * Parse the project from ProjectConversationMessage resource.
     *
     * @param {string} projectConversationMessageName
     *   A fully-qualified path representing project_conversation_message resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectConversationMessageName(projectConversationMessageName) {
        return this.pathTemplates.projectConversationMessagePathTemplate.match(projectConversationMessageName).project;
    }
    /**
     * Parse the conversation from ProjectConversationMessage resource.
     *
     * @param {string} projectConversationMessageName
     *   A fully-qualified path representing project_conversation_message resource.
     * @returns {string} A string representing the conversation.
     */
    matchConversationFromProjectConversationMessageName(projectConversationMessageName) {
        return this.pathTemplates.projectConversationMessagePathTemplate.match(projectConversationMessageName).conversation;
    }
    /**
     * Parse the message from ProjectConversationMessage resource.
     *
     * @param {string} projectConversationMessageName
     *   A fully-qualified path representing project_conversation_message resource.
     * @returns {string} A string representing the message.
     */
    matchMessageFromProjectConversationMessageName(projectConversationMessageName) {
        return this.pathTemplates.projectConversationMessagePathTemplate.match(projectConversationMessageName).message;
    }
    /**
     * Return a fully-qualified projectConversationModel resource name string.
     *
     * @param {string} project
     * @param {string} conversation_model
     * @returns {string} Resource name string.
     */
    projectConversationModelPath(project, conversationModel) {
        return this.pathTemplates.projectConversationModelPathTemplate.render({
            project: project,
            conversation_model: conversationModel,
        });
    }
    /**
     * Parse the project from ProjectConversationModel resource.
     *
     * @param {string} projectConversationModelName
     *   A fully-qualified path representing project_conversation_model resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectConversationModelName(projectConversationModelName) {
        return this.pathTemplates.projectConversationModelPathTemplate.match(projectConversationModelName).project;
    }
    /**
     * Parse the conversation_model from ProjectConversationModel resource.
     *
     * @param {string} projectConversationModelName
     *   A fully-qualified path representing project_conversation_model resource.
     * @returns {string} A string representing the conversation_model.
     */
    matchConversationModelFromProjectConversationModelName(projectConversationModelName) {
        return this.pathTemplates.projectConversationModelPathTemplate.match(projectConversationModelName).conversation_model;
    }
    /**
     * Return a fully-qualified projectConversationModelEvaluation resource name string.
     *
     * @param {string} project
     * @param {string} conversation_model
     * @param {string} evaluation
     * @returns {string} Resource name string.
     */
    projectConversationModelEvaluationPath(project, conversationModel, evaluation) {
        return this.pathTemplates.projectConversationModelEvaluationPathTemplate.render({
            project: project,
            conversation_model: conversationModel,
            evaluation: evaluation,
        });
    }
    /**
     * Parse the project from ProjectConversationModelEvaluation resource.
     *
     * @param {string} projectConversationModelEvaluationName
     *   A fully-qualified path representing project_conversation_model_evaluation resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectConversationModelEvaluationName(projectConversationModelEvaluationName) {
        return this.pathTemplates.projectConversationModelEvaluationPathTemplate.match(projectConversationModelEvaluationName).project;
    }
    /**
     * Parse the conversation_model from ProjectConversationModelEvaluation resource.
     *
     * @param {string} projectConversationModelEvaluationName
     *   A fully-qualified path representing project_conversation_model_evaluation resource.
     * @returns {string} A string representing the conversation_model.
     */
    matchConversationModelFromProjectConversationModelEvaluationName(projectConversationModelEvaluationName) {
        return this.pathTemplates.projectConversationModelEvaluationPathTemplate.match(projectConversationModelEvaluationName).conversation_model;
    }
    /**
     * Parse the evaluation from ProjectConversationModelEvaluation resource.
     *
     * @param {string} projectConversationModelEvaluationName
     *   A fully-qualified path representing project_conversation_model_evaluation resource.
     * @returns {string} A string representing the evaluation.
     */
    matchEvaluationFromProjectConversationModelEvaluationName(projectConversationModelEvaluationName) {
        return this.pathTemplates.projectConversationModelEvaluationPathTemplate.match(projectConversationModelEvaluationName).evaluation;
    }
    /**
     * Return a fully-qualified projectConversationParticipant resource name string.
     *
     * @param {string} project
     * @param {string} conversation
     * @param {string} participant
     * @returns {string} Resource name string.
     */
    projectConversationParticipantPath(project, conversation, participant) {
        return this.pathTemplates.projectConversationParticipantPathTemplate.render({
            project: project,
            conversation: conversation,
            participant: participant,
        });
    }
    /**
     * Parse the project from ProjectConversationParticipant resource.
     *
     * @param {string} projectConversationParticipantName
     *   A fully-qualified path representing project_conversation_participant resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectConversationParticipantName(projectConversationParticipantName) {
        return this.pathTemplates.projectConversationParticipantPathTemplate.match(projectConversationParticipantName).project;
    }
    /**
     * Parse the conversation from ProjectConversationParticipant resource.
     *
     * @param {string} projectConversationParticipantName
     *   A fully-qualified path representing project_conversation_participant resource.
     * @returns {string} A string representing the conversation.
     */
    matchConversationFromProjectConversationParticipantName(projectConversationParticipantName) {
        return this.pathTemplates.projectConversationParticipantPathTemplate.match(projectConversationParticipantName).conversation;
    }
    /**
     * Parse the participant from ProjectConversationParticipant resource.
     *
     * @param {string} projectConversationParticipantName
     *   A fully-qualified path representing project_conversation_participant resource.
     * @returns {string} A string representing the participant.
     */
    matchParticipantFromProjectConversationParticipantName(projectConversationParticipantName) {
        return this.pathTemplates.projectConversationParticipantPathTemplate.match(projectConversationParticipantName).participant;
    }
    /**
     * Return a fully-qualified projectConversationProfile resource name string.
     *
     * @param {string} project
     * @param {string} conversation_profile
     * @returns {string} Resource name string.
     */
    projectConversationProfilePath(project, conversationProfile) {
        return this.pathTemplates.projectConversationProfilePathTemplate.render({
            project: project,
            conversation_profile: conversationProfile,
        });
    }
    /**
     * Parse the project from ProjectConversationProfile resource.
     *
     * @param {string} projectConversationProfileName
     *   A fully-qualified path representing project_conversation_profile resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectConversationProfileName(projectConversationProfileName) {
        return this.pathTemplates.projectConversationProfilePathTemplate.match(projectConversationProfileName).project;
    }
    /**
     * Parse the conversation_profile from ProjectConversationProfile resource.
     *
     * @param {string} projectConversationProfileName
     *   A fully-qualified path representing project_conversation_profile resource.
     * @returns {string} A string representing the conversation_profile.
     */
    matchConversationProfileFromProjectConversationProfileName(projectConversationProfileName) {
        return this.pathTemplates.projectConversationProfilePathTemplate.match(projectConversationProfileName).conversation_profile;
    }
    /**
     * Return a fully-qualified projectKnowledgeBase resource name string.
     *
     * @param {string} project
     * @param {string} knowledge_base
     * @returns {string} Resource name string.
     */
    projectKnowledgeBasePath(project, knowledgeBase) {
        return this.pathTemplates.projectKnowledgeBasePathTemplate.render({
            project: project,
            knowledge_base: knowledgeBase,
        });
    }
    /**
     * Parse the project from ProjectKnowledgeBase resource.
     *
     * @param {string} projectKnowledgeBaseName
     *   A fully-qualified path representing project_knowledge_base resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectKnowledgeBaseName(projectKnowledgeBaseName) {
        return this.pathTemplates.projectKnowledgeBasePathTemplate.match(projectKnowledgeBaseName).project;
    }
    /**
     * Parse the knowledge_base from ProjectKnowledgeBase resource.
     *
     * @param {string} projectKnowledgeBaseName
     *   A fully-qualified path representing project_knowledge_base resource.
     * @returns {string} A string representing the knowledge_base.
     */
    matchKnowledgeBaseFromProjectKnowledgeBaseName(projectKnowledgeBaseName) {
        return this.pathTemplates.projectKnowledgeBasePathTemplate.match(projectKnowledgeBaseName).knowledge_base;
    }
    /**
     * Return a fully-qualified projectKnowledgeBaseDocument resource name string.
     *
     * @param {string} project
     * @param {string} knowledge_base
     * @param {string} document
     * @returns {string} Resource name string.
     */
    projectKnowledgeBaseDocumentPath(project, knowledgeBase, document) {
        return this.pathTemplates.projectKnowledgeBaseDocumentPathTemplate.render({
            project: project,
            knowledge_base: knowledgeBase,
            document: document,
        });
    }
    /**
     * Parse the project from ProjectKnowledgeBaseDocument resource.
     *
     * @param {string} projectKnowledgeBaseDocumentName
     *   A fully-qualified path representing project_knowledge_base_document resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectKnowledgeBaseDocumentName(projectKnowledgeBaseDocumentName) {
        return this.pathTemplates.projectKnowledgeBaseDocumentPathTemplate.match(projectKnowledgeBaseDocumentName).project;
    }
    /**
     * Parse the knowledge_base from ProjectKnowledgeBaseDocument resource.
     *
     * @param {string} projectKnowledgeBaseDocumentName
     *   A fully-qualified path representing project_knowledge_base_document resource.
     * @returns {string} A string representing the knowledge_base.
     */
    matchKnowledgeBaseFromProjectKnowledgeBaseDocumentName(projectKnowledgeBaseDocumentName) {
        return this.pathTemplates.projectKnowledgeBaseDocumentPathTemplate.match(projectKnowledgeBaseDocumentName).knowledge_base;
    }
    /**
     * Parse the document from ProjectKnowledgeBaseDocument resource.
     *
     * @param {string} projectKnowledgeBaseDocumentName
     *   A fully-qualified path representing project_knowledge_base_document resource.
     * @returns {string} A string representing the document.
     */
    matchDocumentFromProjectKnowledgeBaseDocumentName(projectKnowledgeBaseDocumentName) {
        return this.pathTemplates.projectKnowledgeBaseDocumentPathTemplate.match(projectKnowledgeBaseDocumentName).document;
    }
    /**
     * Return a fully-qualified projectLocationAgent resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @returns {string} Resource name string.
     */
    projectLocationAgentPath(project, location) {
        return this.pathTemplates.projectLocationAgentPathTemplate.render({
            project: project,
            location: location,
        });
    }
    /**
     * Parse the project from ProjectLocationAgent resource.
     *
     * @param {string} projectLocationAgentName
     *   A fully-qualified path representing project_location_agent resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationAgentName(projectLocationAgentName) {
        return this.pathTemplates.projectLocationAgentPathTemplate.match(projectLocationAgentName).project;
    }
    /**
     * Parse the location from ProjectLocationAgent resource.
     *
     * @param {string} projectLocationAgentName
     *   A fully-qualified path representing project_location_agent resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationAgentName(projectLocationAgentName) {
        return this.pathTemplates.projectLocationAgentPathTemplate.match(projectLocationAgentName).location;
    }
    /**
     * Return a fully-qualified projectLocationAgentEntityType resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} entity_type
     * @returns {string} Resource name string.
     */
    projectLocationAgentEntityTypePath(project, location, entityType) {
        return this.pathTemplates.projectLocationAgentEntityTypePathTemplate.render({
            project: project,
            location: location,
            entity_type: entityType,
        });
    }
    /**
     * Parse the project from ProjectLocationAgentEntityType resource.
     *
     * @param {string} projectLocationAgentEntityTypeName
     *   A fully-qualified path representing project_location_agent_entity_type resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationAgentEntityTypeName(projectLocationAgentEntityTypeName) {
        return this.pathTemplates.projectLocationAgentEntityTypePathTemplate.match(projectLocationAgentEntityTypeName).project;
    }
    /**
     * Parse the location from ProjectLocationAgentEntityType resource.
     *
     * @param {string} projectLocationAgentEntityTypeName
     *   A fully-qualified path representing project_location_agent_entity_type resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationAgentEntityTypeName(projectLocationAgentEntityTypeName) {
        return this.pathTemplates.projectLocationAgentEntityTypePathTemplate.match(projectLocationAgentEntityTypeName).location;
    }
    /**
     * Parse the entity_type from ProjectLocationAgentEntityType resource.
     *
     * @param {string} projectLocationAgentEntityTypeName
     *   A fully-qualified path representing project_location_agent_entity_type resource.
     * @returns {string} A string representing the entity_type.
     */
    matchEntityTypeFromProjectLocationAgentEntityTypeName(projectLocationAgentEntityTypeName) {
        return this.pathTemplates.projectLocationAgentEntityTypePathTemplate.match(projectLocationAgentEntityTypeName).entity_type;
    }
    /**
     * Return a fully-qualified projectLocationAgentEnvironment resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} environment
     * @returns {string} Resource name string.
     */
    projectLocationAgentEnvironmentPath(project, location, environment) {
        return this.pathTemplates.projectLocationAgentEnvironmentPathTemplate.render({
            project: project,
            location: location,
            environment: environment,
        });
    }
    /**
     * Parse the project from ProjectLocationAgentEnvironment resource.
     *
     * @param {string} projectLocationAgentEnvironmentName
     *   A fully-qualified path representing project_location_agent_environment resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationAgentEnvironmentName(projectLocationAgentEnvironmentName) {
        return this.pathTemplates.projectLocationAgentEnvironmentPathTemplate.match(projectLocationAgentEnvironmentName).project;
    }
    /**
     * Parse the location from ProjectLocationAgentEnvironment resource.
     *
     * @param {string} projectLocationAgentEnvironmentName
     *   A fully-qualified path representing project_location_agent_environment resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationAgentEnvironmentName(projectLocationAgentEnvironmentName) {
        return this.pathTemplates.projectLocationAgentEnvironmentPathTemplate.match(projectLocationAgentEnvironmentName).location;
    }
    /**
     * Parse the environment from ProjectLocationAgentEnvironment resource.
     *
     * @param {string} projectLocationAgentEnvironmentName
     *   A fully-qualified path representing project_location_agent_environment resource.
     * @returns {string} A string representing the environment.
     */
    matchEnvironmentFromProjectLocationAgentEnvironmentName(projectLocationAgentEnvironmentName) {
        return this.pathTemplates.projectLocationAgentEnvironmentPathTemplate.match(projectLocationAgentEnvironmentName).environment;
    }
    /**
     * Return a fully-qualified projectLocationAgentEnvironmentUserSession resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} environment
     * @param {string} user
     * @param {string} session
     * @returns {string} Resource name string.
     */
    projectLocationAgentEnvironmentUserSessionPath(project, location, environment, user, session) {
        return this.pathTemplates.projectLocationAgentEnvironmentUserSessionPathTemplate.render({
            project: project,
            location: location,
            environment: environment,
            user: user,
            session: session,
        });
    }
    /**
     * Parse the project from ProjectLocationAgentEnvironmentUserSession resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionName
     *   A fully-qualified path representing project_location_agent_environment_user_session resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationAgentEnvironmentUserSessionName(projectLocationAgentEnvironmentUserSessionName) {
        return this.pathTemplates.projectLocationAgentEnvironmentUserSessionPathTemplate.match(projectLocationAgentEnvironmentUserSessionName).project;
    }
    /**
     * Parse the location from ProjectLocationAgentEnvironmentUserSession resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionName
     *   A fully-qualified path representing project_location_agent_environment_user_session resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationAgentEnvironmentUserSessionName(projectLocationAgentEnvironmentUserSessionName) {
        return this.pathTemplates.projectLocationAgentEnvironmentUserSessionPathTemplate.match(projectLocationAgentEnvironmentUserSessionName).location;
    }
    /**
     * Parse the environment from ProjectLocationAgentEnvironmentUserSession resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionName
     *   A fully-qualified path representing project_location_agent_environment_user_session resource.
     * @returns {string} A string representing the environment.
     */
    matchEnvironmentFromProjectLocationAgentEnvironmentUserSessionName(projectLocationAgentEnvironmentUserSessionName) {
        return this.pathTemplates.projectLocationAgentEnvironmentUserSessionPathTemplate.match(projectLocationAgentEnvironmentUserSessionName).environment;
    }
    /**
     * Parse the user from ProjectLocationAgentEnvironmentUserSession resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionName
     *   A fully-qualified path representing project_location_agent_environment_user_session resource.
     * @returns {string} A string representing the user.
     */
    matchUserFromProjectLocationAgentEnvironmentUserSessionName(projectLocationAgentEnvironmentUserSessionName) {
        return this.pathTemplates.projectLocationAgentEnvironmentUserSessionPathTemplate.match(projectLocationAgentEnvironmentUserSessionName).user;
    }
    /**
     * Parse the session from ProjectLocationAgentEnvironmentUserSession resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionName
     *   A fully-qualified path representing project_location_agent_environment_user_session resource.
     * @returns {string} A string representing the session.
     */
    matchSessionFromProjectLocationAgentEnvironmentUserSessionName(projectLocationAgentEnvironmentUserSessionName) {
        return this.pathTemplates.projectLocationAgentEnvironmentUserSessionPathTemplate.match(projectLocationAgentEnvironmentUserSessionName).session;
    }
    /**
     * Return a fully-qualified projectLocationAgentEnvironmentUserSessionContext resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} environment
     * @param {string} user
     * @param {string} session
     * @param {string} context
     * @returns {string} Resource name string.
     */
    projectLocationAgentEnvironmentUserSessionContextPath(project, location, environment, user, session, context) {
        return this.pathTemplates.projectLocationAgentEnvironmentUserSessionContextPathTemplate.render({
            project: project,
            location: location,
            environment: environment,
            user: user,
            session: session,
            context: context,
        });
    }
    /**
     * Parse the project from ProjectLocationAgentEnvironmentUserSessionContext resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionContextName
     *   A fully-qualified path representing project_location_agent_environment_user_session_context resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationAgentEnvironmentUserSessionContextName(projectLocationAgentEnvironmentUserSessionContextName) {
        return this.pathTemplates.projectLocationAgentEnvironmentUserSessionContextPathTemplate.match(projectLocationAgentEnvironmentUserSessionContextName).project;
    }
    /**
     * Parse the location from ProjectLocationAgentEnvironmentUserSessionContext resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionContextName
     *   A fully-qualified path representing project_location_agent_environment_user_session_context resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationAgentEnvironmentUserSessionContextName(projectLocationAgentEnvironmentUserSessionContextName) {
        return this.pathTemplates.projectLocationAgentEnvironmentUserSessionContextPathTemplate.match(projectLocationAgentEnvironmentUserSessionContextName).location;
    }
    /**
     * Parse the environment from ProjectLocationAgentEnvironmentUserSessionContext resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionContextName
     *   A fully-qualified path representing project_location_agent_environment_user_session_context resource.
     * @returns {string} A string representing the environment.
     */
    matchEnvironmentFromProjectLocationAgentEnvironmentUserSessionContextName(projectLocationAgentEnvironmentUserSessionContextName) {
        return this.pathTemplates.projectLocationAgentEnvironmentUserSessionContextPathTemplate.match(projectLocationAgentEnvironmentUserSessionContextName).environment;
    }
    /**
     * Parse the user from ProjectLocationAgentEnvironmentUserSessionContext resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionContextName
     *   A fully-qualified path representing project_location_agent_environment_user_session_context resource.
     * @returns {string} A string representing the user.
     */
    matchUserFromProjectLocationAgentEnvironmentUserSessionContextName(projectLocationAgentEnvironmentUserSessionContextName) {
        return this.pathTemplates.projectLocationAgentEnvironmentUserSessionContextPathTemplate.match(projectLocationAgentEnvironmentUserSessionContextName).user;
    }
    /**
     * Parse the session from ProjectLocationAgentEnvironmentUserSessionContext resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionContextName
     *   A fully-qualified path representing project_location_agent_environment_user_session_context resource.
     * @returns {string} A string representing the session.
     */
    matchSessionFromProjectLocationAgentEnvironmentUserSessionContextName(projectLocationAgentEnvironmentUserSessionContextName) {
        return this.pathTemplates.projectLocationAgentEnvironmentUserSessionContextPathTemplate.match(projectLocationAgentEnvironmentUserSessionContextName).session;
    }
    /**
     * Parse the context from ProjectLocationAgentEnvironmentUserSessionContext resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionContextName
     *   A fully-qualified path representing project_location_agent_environment_user_session_context resource.
     * @returns {string} A string representing the context.
     */
    matchContextFromProjectLocationAgentEnvironmentUserSessionContextName(projectLocationAgentEnvironmentUserSessionContextName) {
        return this.pathTemplates.projectLocationAgentEnvironmentUserSessionContextPathTemplate.match(projectLocationAgentEnvironmentUserSessionContextName).context;
    }
    /**
     * Return a fully-qualified projectLocationAgentEnvironmentUserSessionEntityType resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} environment
     * @param {string} user
     * @param {string} session
     * @param {string} entity_type
     * @returns {string} Resource name string.
     */
    projectLocationAgentEnvironmentUserSessionEntityTypePath(project, location, environment, user, session, entityType) {
        return this.pathTemplates.projectLocationAgentEnvironmentUserSessionEntityTypePathTemplate.render({
            project: project,
            location: location,
            environment: environment,
            user: user,
            session: session,
            entity_type: entityType,
        });
    }
    /**
     * Parse the project from ProjectLocationAgentEnvironmentUserSessionEntityType resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionEntityTypeName
     *   A fully-qualified path representing project_location_agent_environment_user_session_entity_type resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationAgentEnvironmentUserSessionEntityTypeName(projectLocationAgentEnvironmentUserSessionEntityTypeName) {
        return this.pathTemplates.projectLocationAgentEnvironmentUserSessionEntityTypePathTemplate.match(projectLocationAgentEnvironmentUserSessionEntityTypeName).project;
    }
    /**
     * Parse the location from ProjectLocationAgentEnvironmentUserSessionEntityType resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionEntityTypeName
     *   A fully-qualified path representing project_location_agent_environment_user_session_entity_type resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationAgentEnvironmentUserSessionEntityTypeName(projectLocationAgentEnvironmentUserSessionEntityTypeName) {
        return this.pathTemplates.projectLocationAgentEnvironmentUserSessionEntityTypePathTemplate.match(projectLocationAgentEnvironmentUserSessionEntityTypeName).location;
    }
    /**
     * Parse the environment from ProjectLocationAgentEnvironmentUserSessionEntityType resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionEntityTypeName
     *   A fully-qualified path representing project_location_agent_environment_user_session_entity_type resource.
     * @returns {string} A string representing the environment.
     */
    matchEnvironmentFromProjectLocationAgentEnvironmentUserSessionEntityTypeName(projectLocationAgentEnvironmentUserSessionEntityTypeName) {
        return this.pathTemplates.projectLocationAgentEnvironmentUserSessionEntityTypePathTemplate.match(projectLocationAgentEnvironmentUserSessionEntityTypeName).environment;
    }
    /**
     * Parse the user from ProjectLocationAgentEnvironmentUserSessionEntityType resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionEntityTypeName
     *   A fully-qualified path representing project_location_agent_environment_user_session_entity_type resource.
     * @returns {string} A string representing the user.
     */
    matchUserFromProjectLocationAgentEnvironmentUserSessionEntityTypeName(projectLocationAgentEnvironmentUserSessionEntityTypeName) {
        return this.pathTemplates.projectLocationAgentEnvironmentUserSessionEntityTypePathTemplate.match(projectLocationAgentEnvironmentUserSessionEntityTypeName).user;
    }
    /**
     * Parse the session from ProjectLocationAgentEnvironmentUserSessionEntityType resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionEntityTypeName
     *   A fully-qualified path representing project_location_agent_environment_user_session_entity_type resource.
     * @returns {string} A string representing the session.
     */
    matchSessionFromProjectLocationAgentEnvironmentUserSessionEntityTypeName(projectLocationAgentEnvironmentUserSessionEntityTypeName) {
        return this.pathTemplates.projectLocationAgentEnvironmentUserSessionEntityTypePathTemplate.match(projectLocationAgentEnvironmentUserSessionEntityTypeName).session;
    }
    /**
     * Parse the entity_type from ProjectLocationAgentEnvironmentUserSessionEntityType resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionEntityTypeName
     *   A fully-qualified path representing project_location_agent_environment_user_session_entity_type resource.
     * @returns {string} A string representing the entity_type.
     */
    matchEntityTypeFromProjectLocationAgentEnvironmentUserSessionEntityTypeName(projectLocationAgentEnvironmentUserSessionEntityTypeName) {
        return this.pathTemplates.projectLocationAgentEnvironmentUserSessionEntityTypePathTemplate.match(projectLocationAgentEnvironmentUserSessionEntityTypeName).entity_type;
    }
    /**
     * Return a fully-qualified projectLocationAgentFulfillment resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @returns {string} Resource name string.
     */
    projectLocationAgentFulfillmentPath(project, location) {
        return this.pathTemplates.projectLocationAgentFulfillmentPathTemplate.render({
            project: project,
            location: location,
        });
    }
    /**
     * Parse the project from ProjectLocationAgentFulfillment resource.
     *
     * @param {string} projectLocationAgentFulfillmentName
     *   A fully-qualified path representing project_location_agent_fulfillment resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationAgentFulfillmentName(projectLocationAgentFulfillmentName) {
        return this.pathTemplates.projectLocationAgentFulfillmentPathTemplate.match(projectLocationAgentFulfillmentName).project;
    }
    /**
     * Parse the location from ProjectLocationAgentFulfillment resource.
     *
     * @param {string} projectLocationAgentFulfillmentName
     *   A fully-qualified path representing project_location_agent_fulfillment resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationAgentFulfillmentName(projectLocationAgentFulfillmentName) {
        return this.pathTemplates.projectLocationAgentFulfillmentPathTemplate.match(projectLocationAgentFulfillmentName).location;
    }
    /**
     * Return a fully-qualified projectLocationAgentIntent resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} intent
     * @returns {string} Resource name string.
     */
    projectLocationAgentIntentPath(project, location, intent) {
        return this.pathTemplates.projectLocationAgentIntentPathTemplate.render({
            project: project,
            location: location,
            intent: intent,
        });
    }
    /**
     * Parse the project from ProjectLocationAgentIntent resource.
     *
     * @param {string} projectLocationAgentIntentName
     *   A fully-qualified path representing project_location_agent_intent resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationAgentIntentName(projectLocationAgentIntentName) {
        return this.pathTemplates.projectLocationAgentIntentPathTemplate.match(projectLocationAgentIntentName).project;
    }
    /**
     * Parse the location from ProjectLocationAgentIntent resource.
     *
     * @param {string} projectLocationAgentIntentName
     *   A fully-qualified path representing project_location_agent_intent resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationAgentIntentName(projectLocationAgentIntentName) {
        return this.pathTemplates.projectLocationAgentIntentPathTemplate.match(projectLocationAgentIntentName).location;
    }
    /**
     * Parse the intent from ProjectLocationAgentIntent resource.
     *
     * @param {string} projectLocationAgentIntentName
     *   A fully-qualified path representing project_location_agent_intent resource.
     * @returns {string} A string representing the intent.
     */
    matchIntentFromProjectLocationAgentIntentName(projectLocationAgentIntentName) {
        return this.pathTemplates.projectLocationAgentIntentPathTemplate.match(projectLocationAgentIntentName).intent;
    }
    /**
     * Return a fully-qualified projectLocationAgentSession resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} session
     * @returns {string} Resource name string.
     */
    projectLocationAgentSessionPath(project, location, session) {
        return this.pathTemplates.projectLocationAgentSessionPathTemplate.render({
            project: project,
            location: location,
            session: session,
        });
    }
    /**
     * Parse the project from ProjectLocationAgentSession resource.
     *
     * @param {string} projectLocationAgentSessionName
     *   A fully-qualified path representing project_location_agent_session resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationAgentSessionName(projectLocationAgentSessionName) {
        return this.pathTemplates.projectLocationAgentSessionPathTemplate.match(projectLocationAgentSessionName).project;
    }
    /**
     * Parse the location from ProjectLocationAgentSession resource.
     *
     * @param {string} projectLocationAgentSessionName
     *   A fully-qualified path representing project_location_agent_session resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationAgentSessionName(projectLocationAgentSessionName) {
        return this.pathTemplates.projectLocationAgentSessionPathTemplate.match(projectLocationAgentSessionName).location;
    }
    /**
     * Parse the session from ProjectLocationAgentSession resource.
     *
     * @param {string} projectLocationAgentSessionName
     *   A fully-qualified path representing project_location_agent_session resource.
     * @returns {string} A string representing the session.
     */
    matchSessionFromProjectLocationAgentSessionName(projectLocationAgentSessionName) {
        return this.pathTemplates.projectLocationAgentSessionPathTemplate.match(projectLocationAgentSessionName).session;
    }
    /**
     * Return a fully-qualified projectLocationAgentSessionContext resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} session
     * @param {string} context
     * @returns {string} Resource name string.
     */
    projectLocationAgentSessionContextPath(project, location, session, context) {
        return this.pathTemplates.projectLocationAgentSessionContextPathTemplate.render({
            project: project,
            location: location,
            session: session,
            context: context,
        });
    }
    /**
     * Parse the project from ProjectLocationAgentSessionContext resource.
     *
     * @param {string} projectLocationAgentSessionContextName
     *   A fully-qualified path representing project_location_agent_session_context resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationAgentSessionContextName(projectLocationAgentSessionContextName) {
        return this.pathTemplates.projectLocationAgentSessionContextPathTemplate.match(projectLocationAgentSessionContextName).project;
    }
    /**
     * Parse the location from ProjectLocationAgentSessionContext resource.
     *
     * @param {string} projectLocationAgentSessionContextName
     *   A fully-qualified path representing project_location_agent_session_context resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationAgentSessionContextName(projectLocationAgentSessionContextName) {
        return this.pathTemplates.projectLocationAgentSessionContextPathTemplate.match(projectLocationAgentSessionContextName).location;
    }
    /**
     * Parse the session from ProjectLocationAgentSessionContext resource.
     *
     * @param {string} projectLocationAgentSessionContextName
     *   A fully-qualified path representing project_location_agent_session_context resource.
     * @returns {string} A string representing the session.
     */
    matchSessionFromProjectLocationAgentSessionContextName(projectLocationAgentSessionContextName) {
        return this.pathTemplates.projectLocationAgentSessionContextPathTemplate.match(projectLocationAgentSessionContextName).session;
    }
    /**
     * Parse the context from ProjectLocationAgentSessionContext resource.
     *
     * @param {string} projectLocationAgentSessionContextName
     *   A fully-qualified path representing project_location_agent_session_context resource.
     * @returns {string} A string representing the context.
     */
    matchContextFromProjectLocationAgentSessionContextName(projectLocationAgentSessionContextName) {
        return this.pathTemplates.projectLocationAgentSessionContextPathTemplate.match(projectLocationAgentSessionContextName).context;
    }
    /**
     * Return a fully-qualified projectLocationAgentSessionEntityType resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} session
     * @param {string} entity_type
     * @returns {string} Resource name string.
     */
    projectLocationAgentSessionEntityTypePath(project, location, session, entityType) {
        return this.pathTemplates.projectLocationAgentSessionEntityTypePathTemplate.render({
            project: project,
            location: location,
            session: session,
            entity_type: entityType,
        });
    }
    /**
     * Parse the project from ProjectLocationAgentSessionEntityType resource.
     *
     * @param {string} projectLocationAgentSessionEntityTypeName
     *   A fully-qualified path representing project_location_agent_session_entity_type resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationAgentSessionEntityTypeName(projectLocationAgentSessionEntityTypeName) {
        return this.pathTemplates.projectLocationAgentSessionEntityTypePathTemplate.match(projectLocationAgentSessionEntityTypeName).project;
    }
    /**
     * Parse the location from ProjectLocationAgentSessionEntityType resource.
     *
     * @param {string} projectLocationAgentSessionEntityTypeName
     *   A fully-qualified path representing project_location_agent_session_entity_type resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationAgentSessionEntityTypeName(projectLocationAgentSessionEntityTypeName) {
        return this.pathTemplates.projectLocationAgentSessionEntityTypePathTemplate.match(projectLocationAgentSessionEntityTypeName).location;
    }
    /**
     * Parse the session from ProjectLocationAgentSessionEntityType resource.
     *
     * @param {string} projectLocationAgentSessionEntityTypeName
     *   A fully-qualified path representing project_location_agent_session_entity_type resource.
     * @returns {string} A string representing the session.
     */
    matchSessionFromProjectLocationAgentSessionEntityTypeName(projectLocationAgentSessionEntityTypeName) {
        return this.pathTemplates.projectLocationAgentSessionEntityTypePathTemplate.match(projectLocationAgentSessionEntityTypeName).session;
    }
    /**
     * Parse the entity_type from ProjectLocationAgentSessionEntityType resource.
     *
     * @param {string} projectLocationAgentSessionEntityTypeName
     *   A fully-qualified path representing project_location_agent_session_entity_type resource.
     * @returns {string} A string representing the entity_type.
     */
    matchEntityTypeFromProjectLocationAgentSessionEntityTypeName(projectLocationAgentSessionEntityTypeName) {
        return this.pathTemplates.projectLocationAgentSessionEntityTypePathTemplate.match(projectLocationAgentSessionEntityTypeName).entity_type;
    }
    /**
     * Return a fully-qualified projectLocationAgentVersion resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} version
     * @returns {string} Resource name string.
     */
    projectLocationAgentVersionPath(project, location, version) {
        return this.pathTemplates.projectLocationAgentVersionPathTemplate.render({
            project: project,
            location: location,
            version: version,
        });
    }
    /**
     * Parse the project from ProjectLocationAgentVersion resource.
     *
     * @param {string} projectLocationAgentVersionName
     *   A fully-qualified path representing project_location_agent_version resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationAgentVersionName(projectLocationAgentVersionName) {
        return this.pathTemplates.projectLocationAgentVersionPathTemplate.match(projectLocationAgentVersionName).project;
    }
    /**
     * Parse the location from ProjectLocationAgentVersion resource.
     *
     * @param {string} projectLocationAgentVersionName
     *   A fully-qualified path representing project_location_agent_version resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationAgentVersionName(projectLocationAgentVersionName) {
        return this.pathTemplates.projectLocationAgentVersionPathTemplate.match(projectLocationAgentVersionName).location;
    }
    /**
     * Parse the version from ProjectLocationAgentVersion resource.
     *
     * @param {string} projectLocationAgentVersionName
     *   A fully-qualified path representing project_location_agent_version resource.
     * @returns {string} A string representing the version.
     */
    matchVersionFromProjectLocationAgentVersionName(projectLocationAgentVersionName) {
        return this.pathTemplates.projectLocationAgentVersionPathTemplate.match(projectLocationAgentVersionName).version;
    }
    /**
     * Return a fully-qualified projectLocationAnswerRecord resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} answer_record
     * @returns {string} Resource name string.
     */
    projectLocationAnswerRecordPath(project, location, answerRecord) {
        return this.pathTemplates.projectLocationAnswerRecordPathTemplate.render({
            project: project,
            location: location,
            answer_record: answerRecord,
        });
    }
    /**
     * Parse the project from ProjectLocationAnswerRecord resource.
     *
     * @param {string} projectLocationAnswerRecordName
     *   A fully-qualified path representing project_location_answer_record resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationAnswerRecordName(projectLocationAnswerRecordName) {
        return this.pathTemplates.projectLocationAnswerRecordPathTemplate.match(projectLocationAnswerRecordName).project;
    }
    /**
     * Parse the location from ProjectLocationAnswerRecord resource.
     *
     * @param {string} projectLocationAnswerRecordName
     *   A fully-qualified path representing project_location_answer_record resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationAnswerRecordName(projectLocationAnswerRecordName) {
        return this.pathTemplates.projectLocationAnswerRecordPathTemplate.match(projectLocationAnswerRecordName).location;
    }
    /**
     * Parse the answer_record from ProjectLocationAnswerRecord resource.
     *
     * @param {string} projectLocationAnswerRecordName
     *   A fully-qualified path representing project_location_answer_record resource.
     * @returns {string} A string representing the answer_record.
     */
    matchAnswerRecordFromProjectLocationAnswerRecordName(projectLocationAnswerRecordName) {
        return this.pathTemplates.projectLocationAnswerRecordPathTemplate.match(projectLocationAnswerRecordName).answer_record;
    }
    /**
     * Return a fully-qualified projectLocationConversation resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} conversation
     * @returns {string} Resource name string.
     */
    projectLocationConversationPath(project, location, conversation) {
        return this.pathTemplates.projectLocationConversationPathTemplate.render({
            project: project,
            location: location,
            conversation: conversation,
        });
    }
    /**
     * Parse the project from ProjectLocationConversation resource.
     *
     * @param {string} projectLocationConversationName
     *   A fully-qualified path representing project_location_conversation resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationConversationName(projectLocationConversationName) {
        return this.pathTemplates.projectLocationConversationPathTemplate.match(projectLocationConversationName).project;
    }
    /**
     * Parse the location from ProjectLocationConversation resource.
     *
     * @param {string} projectLocationConversationName
     *   A fully-qualified path representing project_location_conversation resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationConversationName(projectLocationConversationName) {
        return this.pathTemplates.projectLocationConversationPathTemplate.match(projectLocationConversationName).location;
    }
    /**
     * Parse the conversation from ProjectLocationConversation resource.
     *
     * @param {string} projectLocationConversationName
     *   A fully-qualified path representing project_location_conversation resource.
     * @returns {string} A string representing the conversation.
     */
    matchConversationFromProjectLocationConversationName(projectLocationConversationName) {
        return this.pathTemplates.projectLocationConversationPathTemplate.match(projectLocationConversationName).conversation;
    }
    /**
     * Return a fully-qualified projectLocationConversationMessage resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} conversation
     * @param {string} message
     * @returns {string} Resource name string.
     */
    projectLocationConversationMessagePath(project, location, conversation, message) {
        return this.pathTemplates.projectLocationConversationMessagePathTemplate.render({
            project: project,
            location: location,
            conversation: conversation,
            message: message,
        });
    }
    /**
     * Parse the project from ProjectLocationConversationMessage resource.
     *
     * @param {string} projectLocationConversationMessageName
     *   A fully-qualified path representing project_location_conversation_message resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationConversationMessageName(projectLocationConversationMessageName) {
        return this.pathTemplates.projectLocationConversationMessagePathTemplate.match(projectLocationConversationMessageName).project;
    }
    /**
     * Parse the location from ProjectLocationConversationMessage resource.
     *
     * @param {string} projectLocationConversationMessageName
     *   A fully-qualified path representing project_location_conversation_message resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationConversationMessageName(projectLocationConversationMessageName) {
        return this.pathTemplates.projectLocationConversationMessagePathTemplate.match(projectLocationConversationMessageName).location;
    }
    /**
     * Parse the conversation from ProjectLocationConversationMessage resource.
     *
     * @param {string} projectLocationConversationMessageName
     *   A fully-qualified path representing project_location_conversation_message resource.
     * @returns {string} A string representing the conversation.
     */
    matchConversationFromProjectLocationConversationMessageName(projectLocationConversationMessageName) {
        return this.pathTemplates.projectLocationConversationMessagePathTemplate.match(projectLocationConversationMessageName).conversation;
    }
    /**
     * Parse the message from ProjectLocationConversationMessage resource.
     *
     * @param {string} projectLocationConversationMessageName
     *   A fully-qualified path representing project_location_conversation_message resource.
     * @returns {string} A string representing the message.
     */
    matchMessageFromProjectLocationConversationMessageName(projectLocationConversationMessageName) {
        return this.pathTemplates.projectLocationConversationMessagePathTemplate.match(projectLocationConversationMessageName).message;
    }
    /**
     * Return a fully-qualified projectLocationConversationModel resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} conversation_model
     * @returns {string} Resource name string.
     */
    projectLocationConversationModelPath(project, location, conversationModel) {
        return this.pathTemplates.projectLocationConversationModelPathTemplate.render({
            project: project,
            location: location,
            conversation_model: conversationModel,
        });
    }
    /**
     * Parse the project from ProjectLocationConversationModel resource.
     *
     * @param {string} projectLocationConversationModelName
     *   A fully-qualified path representing project_location_conversation_model resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationConversationModelName(projectLocationConversationModelName) {
        return this.pathTemplates.projectLocationConversationModelPathTemplate.match(projectLocationConversationModelName).project;
    }
    /**
     * Parse the location from ProjectLocationConversationModel resource.
     *
     * @param {string} projectLocationConversationModelName
     *   A fully-qualified path representing project_location_conversation_model resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationConversationModelName(projectLocationConversationModelName) {
        return this.pathTemplates.projectLocationConversationModelPathTemplate.match(projectLocationConversationModelName).location;
    }
    /**
     * Parse the conversation_model from ProjectLocationConversationModel resource.
     *
     * @param {string} projectLocationConversationModelName
     *   A fully-qualified path representing project_location_conversation_model resource.
     * @returns {string} A string representing the conversation_model.
     */
    matchConversationModelFromProjectLocationConversationModelName(projectLocationConversationModelName) {
        return this.pathTemplates.projectLocationConversationModelPathTemplate.match(projectLocationConversationModelName).conversation_model;
    }
    /**
     * Return a fully-qualified projectLocationConversationModelEvaluation resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} conversation_model
     * @param {string} evaluation
     * @returns {string} Resource name string.
     */
    projectLocationConversationModelEvaluationPath(project, location, conversationModel, evaluation) {
        return this.pathTemplates.projectLocationConversationModelEvaluationPathTemplate.render({
            project: project,
            location: location,
            conversation_model: conversationModel,
            evaluation: evaluation,
        });
    }
    /**
     * Parse the project from ProjectLocationConversationModelEvaluation resource.
     *
     * @param {string} projectLocationConversationModelEvaluationName
     *   A fully-qualified path representing project_location_conversation_model_evaluation resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationConversationModelEvaluationName(projectLocationConversationModelEvaluationName) {
        return this.pathTemplates.projectLocationConversationModelEvaluationPathTemplate.match(projectLocationConversationModelEvaluationName).project;
    }
    /**
     * Parse the location from ProjectLocationConversationModelEvaluation resource.
     *
     * @param {string} projectLocationConversationModelEvaluationName
     *   A fully-qualified path representing project_location_conversation_model_evaluation resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationConversationModelEvaluationName(projectLocationConversationModelEvaluationName) {
        return this.pathTemplates.projectLocationConversationModelEvaluationPathTemplate.match(projectLocationConversationModelEvaluationName).location;
    }
    /**
     * Parse the conversation_model from ProjectLocationConversationModelEvaluation resource.
     *
     * @param {string} projectLocationConversationModelEvaluationName
     *   A fully-qualified path representing project_location_conversation_model_evaluation resource.
     * @returns {string} A string representing the conversation_model.
     */
    matchConversationModelFromProjectLocationConversationModelEvaluationName(projectLocationConversationModelEvaluationName) {
        return this.pathTemplates.projectLocationConversationModelEvaluationPathTemplate.match(projectLocationConversationModelEvaluationName).conversation_model;
    }
    /**
     * Parse the evaluation from ProjectLocationConversationModelEvaluation resource.
     *
     * @param {string} projectLocationConversationModelEvaluationName
     *   A fully-qualified path representing project_location_conversation_model_evaluation resource.
     * @returns {string} A string representing the evaluation.
     */
    matchEvaluationFromProjectLocationConversationModelEvaluationName(projectLocationConversationModelEvaluationName) {
        return this.pathTemplates.projectLocationConversationModelEvaluationPathTemplate.match(projectLocationConversationModelEvaluationName).evaluation;
    }
    /**
     * Return a fully-qualified projectLocationConversationParticipant resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} conversation
     * @param {string} participant
     * @returns {string} Resource name string.
     */
    projectLocationConversationParticipantPath(project, location, conversation, participant) {
        return this.pathTemplates.projectLocationConversationParticipantPathTemplate.render({
            project: project,
            location: location,
            conversation: conversation,
            participant: participant,
        });
    }
    /**
     * Parse the project from ProjectLocationConversationParticipant resource.
     *
     * @param {string} projectLocationConversationParticipantName
     *   A fully-qualified path representing project_location_conversation_participant resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationConversationParticipantName(projectLocationConversationParticipantName) {
        return this.pathTemplates.projectLocationConversationParticipantPathTemplate.match(projectLocationConversationParticipantName).project;
    }
    /**
     * Parse the location from ProjectLocationConversationParticipant resource.
     *
     * @param {string} projectLocationConversationParticipantName
     *   A fully-qualified path representing project_location_conversation_participant resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationConversationParticipantName(projectLocationConversationParticipantName) {
        return this.pathTemplates.projectLocationConversationParticipantPathTemplate.match(projectLocationConversationParticipantName).location;
    }
    /**
     * Parse the conversation from ProjectLocationConversationParticipant resource.
     *
     * @param {string} projectLocationConversationParticipantName
     *   A fully-qualified path representing project_location_conversation_participant resource.
     * @returns {string} A string representing the conversation.
     */
    matchConversationFromProjectLocationConversationParticipantName(projectLocationConversationParticipantName) {
        return this.pathTemplates.projectLocationConversationParticipantPathTemplate.match(projectLocationConversationParticipantName).conversation;
    }
    /**
     * Parse the participant from ProjectLocationConversationParticipant resource.
     *
     * @param {string} projectLocationConversationParticipantName
     *   A fully-qualified path representing project_location_conversation_participant resource.
     * @returns {string} A string representing the participant.
     */
    matchParticipantFromProjectLocationConversationParticipantName(projectLocationConversationParticipantName) {
        return this.pathTemplates.projectLocationConversationParticipantPathTemplate.match(projectLocationConversationParticipantName).participant;
    }
    /**
     * Return a fully-qualified projectLocationConversationProfile resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} conversation_profile
     * @returns {string} Resource name string.
     */
    projectLocationConversationProfilePath(project, location, conversationProfile) {
        return this.pathTemplates.projectLocationConversationProfilePathTemplate.render({
            project: project,
            location: location,
            conversation_profile: conversationProfile,
        });
    }
    /**
     * Parse the project from ProjectLocationConversationProfile resource.
     *
     * @param {string} projectLocationConversationProfileName
     *   A fully-qualified path representing project_location_conversation_profile resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationConversationProfileName(projectLocationConversationProfileName) {
        return this.pathTemplates.projectLocationConversationProfilePathTemplate.match(projectLocationConversationProfileName).project;
    }
    /**
     * Parse the location from ProjectLocationConversationProfile resource.
     *
     * @param {string} projectLocationConversationProfileName
     *   A fully-qualified path representing project_location_conversation_profile resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationConversationProfileName(projectLocationConversationProfileName) {
        return this.pathTemplates.projectLocationConversationProfilePathTemplate.match(projectLocationConversationProfileName).location;
    }
    /**
     * Parse the conversation_profile from ProjectLocationConversationProfile resource.
     *
     * @param {string} projectLocationConversationProfileName
     *   A fully-qualified path representing project_location_conversation_profile resource.
     * @returns {string} A string representing the conversation_profile.
     */
    matchConversationProfileFromProjectLocationConversationProfileName(projectLocationConversationProfileName) {
        return this.pathTemplates.projectLocationConversationProfilePathTemplate.match(projectLocationConversationProfileName).conversation_profile;
    }
    /**
     * Return a fully-qualified projectLocationKnowledgeBase resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} knowledge_base
     * @returns {string} Resource name string.
     */
    projectLocationKnowledgeBasePath(project, location, knowledgeBase) {
        return this.pathTemplates.projectLocationKnowledgeBasePathTemplate.render({
            project: project,
            location: location,
            knowledge_base: knowledgeBase,
        });
    }
    /**
     * Parse the project from ProjectLocationKnowledgeBase resource.
     *
     * @param {string} projectLocationKnowledgeBaseName
     *   A fully-qualified path representing project_location_knowledge_base resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationKnowledgeBaseName(projectLocationKnowledgeBaseName) {
        return this.pathTemplates.projectLocationKnowledgeBasePathTemplate.match(projectLocationKnowledgeBaseName).project;
    }
    /**
     * Parse the location from ProjectLocationKnowledgeBase resource.
     *
     * @param {string} projectLocationKnowledgeBaseName
     *   A fully-qualified path representing project_location_knowledge_base resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationKnowledgeBaseName(projectLocationKnowledgeBaseName) {
        return this.pathTemplates.projectLocationKnowledgeBasePathTemplate.match(projectLocationKnowledgeBaseName).location;
    }
    /**
     * Parse the knowledge_base from ProjectLocationKnowledgeBase resource.
     *
     * @param {string} projectLocationKnowledgeBaseName
     *   A fully-qualified path representing project_location_knowledge_base resource.
     * @returns {string} A string representing the knowledge_base.
     */
    matchKnowledgeBaseFromProjectLocationKnowledgeBaseName(projectLocationKnowledgeBaseName) {
        return this.pathTemplates.projectLocationKnowledgeBasePathTemplate.match(projectLocationKnowledgeBaseName).knowledge_base;
    }
    /**
     * Return a fully-qualified projectLocationKnowledgeBaseDocument resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} knowledge_base
     * @param {string} document
     * @returns {string} Resource name string.
     */
    projectLocationKnowledgeBaseDocumentPath(project, location, knowledgeBase, document) {
        return this.pathTemplates.projectLocationKnowledgeBaseDocumentPathTemplate.render({
            project: project,
            location: location,
            knowledge_base: knowledgeBase,
            document: document,
        });
    }
    /**
     * Parse the project from ProjectLocationKnowledgeBaseDocument resource.
     *
     * @param {string} projectLocationKnowledgeBaseDocumentName
     *   A fully-qualified path representing project_location_knowledge_base_document resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationKnowledgeBaseDocumentName(projectLocationKnowledgeBaseDocumentName) {
        return this.pathTemplates.projectLocationKnowledgeBaseDocumentPathTemplate.match(projectLocationKnowledgeBaseDocumentName).project;
    }
    /**
     * Parse the location from ProjectLocationKnowledgeBaseDocument resource.
     *
     * @param {string} projectLocationKnowledgeBaseDocumentName
     *   A fully-qualified path representing project_location_knowledge_base_document resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationKnowledgeBaseDocumentName(projectLocationKnowledgeBaseDocumentName) {
        return this.pathTemplates.projectLocationKnowledgeBaseDocumentPathTemplate.match(projectLocationKnowledgeBaseDocumentName).location;
    }
    /**
     * Parse the knowledge_base from ProjectLocationKnowledgeBaseDocument resource.
     *
     * @param {string} projectLocationKnowledgeBaseDocumentName
     *   A fully-qualified path representing project_location_knowledge_base_document resource.
     * @returns {string} A string representing the knowledge_base.
     */
    matchKnowledgeBaseFromProjectLocationKnowledgeBaseDocumentName(projectLocationKnowledgeBaseDocumentName) {
        return this.pathTemplates.projectLocationKnowledgeBaseDocumentPathTemplate.match(projectLocationKnowledgeBaseDocumentName).knowledge_base;
    }
    /**
     * Parse the document from ProjectLocationKnowledgeBaseDocument resource.
     *
     * @param {string} projectLocationKnowledgeBaseDocumentName
     *   A fully-qualified path representing project_location_knowledge_base_document resource.
     * @returns {string} A string representing the document.
     */
    matchDocumentFromProjectLocationKnowledgeBaseDocumentName(projectLocationKnowledgeBaseDocumentName) {
        return this.pathTemplates.projectLocationKnowledgeBaseDocumentPathTemplate.match(projectLocationKnowledgeBaseDocumentName).document;
    }
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close() {
        if (this.sessionsStub && !this._terminated) {
            return this.sessionsStub.then(stub => {
                this._terminated = true;
                stub.close();
                this.locationsClient.close();
            });
        }
        return Promise.resolve();
    }
}
exports.SessionsClient = SessionsClient;
//# sourceMappingURL=sessions_client.js.map