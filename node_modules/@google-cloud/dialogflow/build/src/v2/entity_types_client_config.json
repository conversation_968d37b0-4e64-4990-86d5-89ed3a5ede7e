{"interfaces": {"google.cloud.dialogflow.v2.EntityTypes": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"], "unavailable": ["UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"ListEntityTypes": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "GetEntityType": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "CreateEntityType": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "UpdateEntityType": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "DeleteEntityType": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "BatchUpdateEntityTypes": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "BatchDeleteEntityTypes": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "BatchCreateEntities": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "BatchUpdateEntities": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "BatchDeleteEntities": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}}}}}