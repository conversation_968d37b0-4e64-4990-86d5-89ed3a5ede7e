import type * as gax from 'google-gax';
import type { Callback, CallOptions, Descriptors, ClientOptions, PaginationCallback, LocationsClient, LocationProtos } from 'google-gax';
import { Transform } from 'stream';
import * as protos from '../../protos/protos';
/**
 *  Service for managing
 *  {@link protos.google.cloud.dialogflow.v2.KnowledgeBase|KnowledgeBases}.
 * @class
 * @memberof v2
 */
export declare class KnowledgeBasesClient {
    private _terminated;
    private _opts;
    private _providedCustomServicePath;
    private _gaxModule;
    private _gaxGrpc;
    private _protos;
    private _defaults;
    private _universeDomain;
    private _servicePath;
    auth: gax.GoogleAuth;
    descriptors: Descriptors;
    warn: (code: string, message: string, warnType?: string) => void;
    innerApiCalls: {
        [name: string]: Function;
    };
    locationsClient: LocationsClient;
    pathTemplates: {
        [name: string]: gax.PathTemplate;
    };
    knowledgeBasesStub?: Promise<{
        [name: string]: Function;
    }>;
    /**
     * Construct an instance of KnowledgeBasesClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://developers.google.com/identity/protocols/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new KnowledgeBasesClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts?: ClientOptions, gaxInstance?: typeof gax | typeof gax.fallback);
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize(): Promise<{
        [name: string]: Function;
    }>;
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath(): string;
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint(): string;
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint(): string;
    get universeDomain(): string;
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port(): number;
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes(): string[];
    getProjectId(): Promise<string>;
    getProjectId(callback: Callback<string, undefined, undefined>): void;
    /**
     * Retrieves the specified knowledge base.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the knowledge base to retrieve.
     *   Format `projects/<Project ID>/locations/<Location
     *   ID>/knowledgeBases/<Knowledge Base ID>`.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.dialogflow.v2.KnowledgeBase|KnowledgeBase}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v2/knowledge_bases.get_knowledge_base.js</caption>
     * region_tag:dialogflow_v2_generated_KnowledgeBases_GetKnowledgeBase_async
     */
    getKnowledgeBase(request?: protos.google.cloud.dialogflow.v2.IGetKnowledgeBaseRequest, options?: CallOptions): Promise<[
        protos.google.cloud.dialogflow.v2.IKnowledgeBase,
        protos.google.cloud.dialogflow.v2.IGetKnowledgeBaseRequest | undefined,
        {} | undefined
    ]>;
    getKnowledgeBase(request: protos.google.cloud.dialogflow.v2.IGetKnowledgeBaseRequest, options: CallOptions, callback: Callback<protos.google.cloud.dialogflow.v2.IKnowledgeBase, protos.google.cloud.dialogflow.v2.IGetKnowledgeBaseRequest | null | undefined, {} | null | undefined>): void;
    getKnowledgeBase(request: protos.google.cloud.dialogflow.v2.IGetKnowledgeBaseRequest, callback: Callback<protos.google.cloud.dialogflow.v2.IKnowledgeBase, protos.google.cloud.dialogflow.v2.IGetKnowledgeBaseRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Creates a knowledge base.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project to create a knowledge base for.
     *   Format: `projects/<Project ID>/locations/<Location ID>`.
     * @param {google.cloud.dialogflow.v2.KnowledgeBase} request.knowledgeBase
     *   Required. The knowledge base to create.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.dialogflow.v2.KnowledgeBase|KnowledgeBase}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v2/knowledge_bases.create_knowledge_base.js</caption>
     * region_tag:dialogflow_v2_generated_KnowledgeBases_CreateKnowledgeBase_async
     */
    createKnowledgeBase(request?: protos.google.cloud.dialogflow.v2.ICreateKnowledgeBaseRequest, options?: CallOptions): Promise<[
        protos.google.cloud.dialogflow.v2.IKnowledgeBase,
        protos.google.cloud.dialogflow.v2.ICreateKnowledgeBaseRequest | undefined,
        {} | undefined
    ]>;
    createKnowledgeBase(request: protos.google.cloud.dialogflow.v2.ICreateKnowledgeBaseRequest, options: CallOptions, callback: Callback<protos.google.cloud.dialogflow.v2.IKnowledgeBase, protos.google.cloud.dialogflow.v2.ICreateKnowledgeBaseRequest | null | undefined, {} | null | undefined>): void;
    createKnowledgeBase(request: protos.google.cloud.dialogflow.v2.ICreateKnowledgeBaseRequest, callback: Callback<protos.google.cloud.dialogflow.v2.IKnowledgeBase, protos.google.cloud.dialogflow.v2.ICreateKnowledgeBaseRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Deletes the specified knowledge base.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the knowledge base to delete.
     *   Format: `projects/<Project ID>/locations/<Location
     *   ID>/knowledgeBases/<Knowledge Base ID>`.
     * @param {boolean} [request.force]
     *   Optional. Force deletes the knowledge base. When set to true, any documents
     *   in the knowledge base are also deleted.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.protobuf.Empty|Empty}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v2/knowledge_bases.delete_knowledge_base.js</caption>
     * region_tag:dialogflow_v2_generated_KnowledgeBases_DeleteKnowledgeBase_async
     */
    deleteKnowledgeBase(request?: protos.google.cloud.dialogflow.v2.IDeleteKnowledgeBaseRequest, options?: CallOptions): Promise<[
        protos.google.protobuf.IEmpty,
        protos.google.cloud.dialogflow.v2.IDeleteKnowledgeBaseRequest | undefined,
        {} | undefined
    ]>;
    deleteKnowledgeBase(request: protos.google.cloud.dialogflow.v2.IDeleteKnowledgeBaseRequest, options: CallOptions, callback: Callback<protos.google.protobuf.IEmpty, protos.google.cloud.dialogflow.v2.IDeleteKnowledgeBaseRequest | null | undefined, {} | null | undefined>): void;
    deleteKnowledgeBase(request: protos.google.cloud.dialogflow.v2.IDeleteKnowledgeBaseRequest, callback: Callback<protos.google.protobuf.IEmpty, protos.google.cloud.dialogflow.v2.IDeleteKnowledgeBaseRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Updates the specified knowledge base.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {google.cloud.dialogflow.v2.KnowledgeBase} request.knowledgeBase
     *   Required. The knowledge base to update.
     * @param {google.protobuf.FieldMask} [request.updateMask]
     *   Optional. Not specified means `update all`.
     *   Currently, only `display_name` can be updated, an InvalidArgument will be
     *   returned for attempting to update other fields.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.dialogflow.v2.KnowledgeBase|KnowledgeBase}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v2/knowledge_bases.update_knowledge_base.js</caption>
     * region_tag:dialogflow_v2_generated_KnowledgeBases_UpdateKnowledgeBase_async
     */
    updateKnowledgeBase(request?: protos.google.cloud.dialogflow.v2.IUpdateKnowledgeBaseRequest, options?: CallOptions): Promise<[
        protos.google.cloud.dialogflow.v2.IKnowledgeBase,
        protos.google.cloud.dialogflow.v2.IUpdateKnowledgeBaseRequest | undefined,
        {} | undefined
    ]>;
    updateKnowledgeBase(request: protos.google.cloud.dialogflow.v2.IUpdateKnowledgeBaseRequest, options: CallOptions, callback: Callback<protos.google.cloud.dialogflow.v2.IKnowledgeBase, protos.google.cloud.dialogflow.v2.IUpdateKnowledgeBaseRequest | null | undefined, {} | null | undefined>): void;
    updateKnowledgeBase(request: protos.google.cloud.dialogflow.v2.IUpdateKnowledgeBaseRequest, callback: Callback<protos.google.cloud.dialogflow.v2.IKnowledgeBase, protos.google.cloud.dialogflow.v2.IUpdateKnowledgeBaseRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Returns the list of all knowledge bases of the specified agent.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project to list of knowledge bases for.
     *   Format: `projects/<Project ID>/locations/<Location ID>`.
     * @param {number} request.pageSize
     *   The maximum number of items to return in a single page. By
     *   default 10 and at most 100.
     * @param {string} request.pageToken
     *   The next_page_token value returned from a previous list request.
     * @param {string} request.filter
     *   The filter expression used to filter knowledge bases returned by the list
     *   method. The expression has the following syntax:
     *
     *     <field> <operator> <value> [AND <field> <operator> <value>] ...
     *
     *   The following fields and operators are supported:
     *
     *   * display_name with has(:) operator
     *   * language_code with equals(=) operator
     *
     *   Examples:
     *
     *   * 'language_code=en-us' matches knowledge bases with en-us language code.
     *   * 'display_name:articles' matches knowledge bases whose display name
     *     contains "articles".
     *   * 'display_name:"Best Articles"' matches knowledge bases whose display
     *     name contains "Best Articles".
     *   * 'language_code=en-gb AND display_name=articles' matches all knowledge
     *     bases whose display name contains "articles" and whose language code is
     *     "en-gb".
     *
     *   Note: An empty filter string (i.e. "") is a no-op and will result in no
     *   filtering.
     *
     *   For more information about filtering, see
     *   [API Filtering](https://aip.dev/160).
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is Array of {@link protos.google.cloud.dialogflow.v2.KnowledgeBase|KnowledgeBase}.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed and will merge results from all the pages into this array.
     *   Note that it can affect your quota.
     *   We recommend using `listKnowledgeBasesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listKnowledgeBases(request?: protos.google.cloud.dialogflow.v2.IListKnowledgeBasesRequest, options?: CallOptions): Promise<[
        protos.google.cloud.dialogflow.v2.IKnowledgeBase[],
        protos.google.cloud.dialogflow.v2.IListKnowledgeBasesRequest | null,
        protos.google.cloud.dialogflow.v2.IListKnowledgeBasesResponse
    ]>;
    listKnowledgeBases(request: protos.google.cloud.dialogflow.v2.IListKnowledgeBasesRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.dialogflow.v2.IListKnowledgeBasesRequest, protos.google.cloud.dialogflow.v2.IListKnowledgeBasesResponse | null | undefined, protos.google.cloud.dialogflow.v2.IKnowledgeBase>): void;
    listKnowledgeBases(request: protos.google.cloud.dialogflow.v2.IListKnowledgeBasesRequest, callback: PaginationCallback<protos.google.cloud.dialogflow.v2.IListKnowledgeBasesRequest, protos.google.cloud.dialogflow.v2.IListKnowledgeBasesResponse | null | undefined, protos.google.cloud.dialogflow.v2.IKnowledgeBase>): void;
    /**
     * Equivalent to `method.name.toCamelCase()`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project to list of knowledge bases for.
     *   Format: `projects/<Project ID>/locations/<Location ID>`.
     * @param {number} request.pageSize
     *   The maximum number of items to return in a single page. By
     *   default 10 and at most 100.
     * @param {string} request.pageToken
     *   The next_page_token value returned from a previous list request.
     * @param {string} request.filter
     *   The filter expression used to filter knowledge bases returned by the list
     *   method. The expression has the following syntax:
     *
     *     <field> <operator> <value> [AND <field> <operator> <value>] ...
     *
     *   The following fields and operators are supported:
     *
     *   * display_name with has(:) operator
     *   * language_code with equals(=) operator
     *
     *   Examples:
     *
     *   * 'language_code=en-us' matches knowledge bases with en-us language code.
     *   * 'display_name:articles' matches knowledge bases whose display name
     *     contains "articles".
     *   * 'display_name:"Best Articles"' matches knowledge bases whose display
     *     name contains "Best Articles".
     *   * 'language_code=en-gb AND display_name=articles' matches all knowledge
     *     bases whose display name contains "articles" and whose language code is
     *     "en-gb".
     *
     *   Note: An empty filter string (i.e. "") is a no-op and will result in no
     *   filtering.
     *
     *   For more information about filtering, see
     *   [API Filtering](https://aip.dev/160).
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.dialogflow.v2.KnowledgeBase|KnowledgeBase} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listKnowledgeBasesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listKnowledgeBasesStream(request?: protos.google.cloud.dialogflow.v2.IListKnowledgeBasesRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listKnowledgeBases`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project to list of knowledge bases for.
     *   Format: `projects/<Project ID>/locations/<Location ID>`.
     * @param {number} request.pageSize
     *   The maximum number of items to return in a single page. By
     *   default 10 and at most 100.
     * @param {string} request.pageToken
     *   The next_page_token value returned from a previous list request.
     * @param {string} request.filter
     *   The filter expression used to filter knowledge bases returned by the list
     *   method. The expression has the following syntax:
     *
     *     <field> <operator> <value> [AND <field> <operator> <value>] ...
     *
     *   The following fields and operators are supported:
     *
     *   * display_name with has(:) operator
     *   * language_code with equals(=) operator
     *
     *   Examples:
     *
     *   * 'language_code=en-us' matches knowledge bases with en-us language code.
     *   * 'display_name:articles' matches knowledge bases whose display name
     *     contains "articles".
     *   * 'display_name:"Best Articles"' matches knowledge bases whose display
     *     name contains "Best Articles".
     *   * 'language_code=en-gb AND display_name=articles' matches all knowledge
     *     bases whose display name contains "articles" and whose language code is
     *     "en-gb".
     *
     *   Note: An empty filter string (i.e. "") is a no-op and will result in no
     *   filtering.
     *
     *   For more information about filtering, see
     *   [API Filtering](https://aip.dev/160).
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.dialogflow.v2.KnowledgeBase|KnowledgeBase}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v2/knowledge_bases.list_knowledge_bases.js</caption>
     * region_tag:dialogflow_v2_generated_KnowledgeBases_ListKnowledgeBases_async
     */
    listKnowledgeBasesAsync(request?: protos.google.cloud.dialogflow.v2.IListKnowledgeBasesRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.dialogflow.v2.IKnowledgeBase>;
    /**
     * Gets information about a location.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Resource name for the location.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html | CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.cloud.location.Location | Location}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example
     * ```
     * const [response] = await client.getLocation(request);
     * ```
     */
    getLocation(request: LocationProtos.google.cloud.location.IGetLocationRequest, options?: gax.CallOptions | Callback<LocationProtos.google.cloud.location.ILocation, LocationProtos.google.cloud.location.IGetLocationRequest | null | undefined, {} | null | undefined>, callback?: Callback<LocationProtos.google.cloud.location.ILocation, LocationProtos.google.cloud.location.IGetLocationRequest | null | undefined, {} | null | undefined>): Promise<LocationProtos.google.cloud.location.ILocation>;
    /**
     * Lists information about the supported locations for this service. Returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   The resource that owns the locations collection, if applicable.
     * @param {string} request.filter
     *   The standard list filter.
     * @param {number} request.pageSize
     *   The standard list page size.
     * @param {string} request.pageToken
     *   The standard list page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link google.cloud.location.Location | Location}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example
     * ```
     * const iterable = client.listLocationsAsync(request);
     * for await (const response of iterable) {
     *   // process response
     * }
     * ```
     */
    listLocationsAsync(request: LocationProtos.google.cloud.location.IListLocationsRequest, options?: CallOptions): AsyncIterable<LocationProtos.google.cloud.location.ILocation>;
    /**
     * Return a fully-qualified conversationDataset resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} conversation_dataset
     * @returns {string} Resource name string.
     */
    conversationDatasetPath(project: string, location: string, conversationDataset: string): string;
    /**
     * Parse the project from ConversationDataset resource.
     *
     * @param {string} conversationDatasetName
     *   A fully-qualified path representing ConversationDataset resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromConversationDatasetName(conversationDatasetName: string): string | number;
    /**
     * Parse the location from ConversationDataset resource.
     *
     * @param {string} conversationDatasetName
     *   A fully-qualified path representing ConversationDataset resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromConversationDatasetName(conversationDatasetName: string): string | number;
    /**
     * Parse the conversation_dataset from ConversationDataset resource.
     *
     * @param {string} conversationDatasetName
     *   A fully-qualified path representing ConversationDataset resource.
     * @returns {string} A string representing the conversation_dataset.
     */
    matchConversationDatasetFromConversationDatasetName(conversationDatasetName: string): string | number;
    /**
     * Return a fully-qualified encryptionSpec resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @returns {string} Resource name string.
     */
    encryptionSpecPath(project: string, location: string): string;
    /**
     * Parse the project from EncryptionSpec resource.
     *
     * @param {string} encryptionSpecName
     *   A fully-qualified path representing EncryptionSpec resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromEncryptionSpecName(encryptionSpecName: string): string | number;
    /**
     * Parse the location from EncryptionSpec resource.
     *
     * @param {string} encryptionSpecName
     *   A fully-qualified path representing EncryptionSpec resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromEncryptionSpecName(encryptionSpecName: string): string | number;
    /**
     * Return a fully-qualified generator resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} generator
     * @returns {string} Resource name string.
     */
    generatorPath(project: string, location: string, generator: string): string;
    /**
     * Parse the project from Generator resource.
     *
     * @param {string} generatorName
     *   A fully-qualified path representing Generator resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromGeneratorName(generatorName: string): string | number;
    /**
     * Parse the location from Generator resource.
     *
     * @param {string} generatorName
     *   A fully-qualified path representing Generator resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromGeneratorName(generatorName: string): string | number;
    /**
     * Parse the generator from Generator resource.
     *
     * @param {string} generatorName
     *   A fully-qualified path representing Generator resource.
     * @returns {string} A string representing the generator.
     */
    matchGeneratorFromGeneratorName(generatorName: string): string | number;
    /**
     * Return a fully-qualified project resource name string.
     *
     * @param {string} project
     * @returns {string} Resource name string.
     */
    projectPath(project: string): string;
    /**
     * Parse the project from Project resource.
     *
     * @param {string} projectName
     *   A fully-qualified path representing Project resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectName(projectName: string): string | number;
    /**
     * Return a fully-qualified projectAgent resource name string.
     *
     * @param {string} project
     * @returns {string} Resource name string.
     */
    projectAgentPath(project: string): string;
    /**
     * Parse the project from ProjectAgent resource.
     *
     * @param {string} projectAgentName
     *   A fully-qualified path representing project_agent resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectAgentName(projectAgentName: string): string | number;
    /**
     * Return a fully-qualified projectAgentEntityType resource name string.
     *
     * @param {string} project
     * @param {string} entity_type
     * @returns {string} Resource name string.
     */
    projectAgentEntityTypePath(project: string, entityType: string): string;
    /**
     * Parse the project from ProjectAgentEntityType resource.
     *
     * @param {string} projectAgentEntityTypeName
     *   A fully-qualified path representing project_agent_entity_type resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectAgentEntityTypeName(projectAgentEntityTypeName: string): string | number;
    /**
     * Parse the entity_type from ProjectAgentEntityType resource.
     *
     * @param {string} projectAgentEntityTypeName
     *   A fully-qualified path representing project_agent_entity_type resource.
     * @returns {string} A string representing the entity_type.
     */
    matchEntityTypeFromProjectAgentEntityTypeName(projectAgentEntityTypeName: string): string | number;
    /**
     * Return a fully-qualified projectAgentEnvironment resource name string.
     *
     * @param {string} project
     * @param {string} environment
     * @returns {string} Resource name string.
     */
    projectAgentEnvironmentPath(project: string, environment: string): string;
    /**
     * Parse the project from ProjectAgentEnvironment resource.
     *
     * @param {string} projectAgentEnvironmentName
     *   A fully-qualified path representing project_agent_environment resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectAgentEnvironmentName(projectAgentEnvironmentName: string): string | number;
    /**
     * Parse the environment from ProjectAgentEnvironment resource.
     *
     * @param {string} projectAgentEnvironmentName
     *   A fully-qualified path representing project_agent_environment resource.
     * @returns {string} A string representing the environment.
     */
    matchEnvironmentFromProjectAgentEnvironmentName(projectAgentEnvironmentName: string): string | number;
    /**
     * Return a fully-qualified projectAgentEnvironmentUserSessionContext resource name string.
     *
     * @param {string} project
     * @param {string} environment
     * @param {string} user
     * @param {string} session
     * @param {string} context
     * @returns {string} Resource name string.
     */
    projectAgentEnvironmentUserSessionContextPath(project: string, environment: string, user: string, session: string, context: string): string;
    /**
     * Parse the project from ProjectAgentEnvironmentUserSessionContext resource.
     *
     * @param {string} projectAgentEnvironmentUserSessionContextName
     *   A fully-qualified path representing project_agent_environment_user_session_context resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectAgentEnvironmentUserSessionContextName(projectAgentEnvironmentUserSessionContextName: string): string | number;
    /**
     * Parse the environment from ProjectAgentEnvironmentUserSessionContext resource.
     *
     * @param {string} projectAgentEnvironmentUserSessionContextName
     *   A fully-qualified path representing project_agent_environment_user_session_context resource.
     * @returns {string} A string representing the environment.
     */
    matchEnvironmentFromProjectAgentEnvironmentUserSessionContextName(projectAgentEnvironmentUserSessionContextName: string): string | number;
    /**
     * Parse the user from ProjectAgentEnvironmentUserSessionContext resource.
     *
     * @param {string} projectAgentEnvironmentUserSessionContextName
     *   A fully-qualified path representing project_agent_environment_user_session_context resource.
     * @returns {string} A string representing the user.
     */
    matchUserFromProjectAgentEnvironmentUserSessionContextName(projectAgentEnvironmentUserSessionContextName: string): string | number;
    /**
     * Parse the session from ProjectAgentEnvironmentUserSessionContext resource.
     *
     * @param {string} projectAgentEnvironmentUserSessionContextName
     *   A fully-qualified path representing project_agent_environment_user_session_context resource.
     * @returns {string} A string representing the session.
     */
    matchSessionFromProjectAgentEnvironmentUserSessionContextName(projectAgentEnvironmentUserSessionContextName: string): string | number;
    /**
     * Parse the context from ProjectAgentEnvironmentUserSessionContext resource.
     *
     * @param {string} projectAgentEnvironmentUserSessionContextName
     *   A fully-qualified path representing project_agent_environment_user_session_context resource.
     * @returns {string} A string representing the context.
     */
    matchContextFromProjectAgentEnvironmentUserSessionContextName(projectAgentEnvironmentUserSessionContextName: string): string | number;
    /**
     * Return a fully-qualified projectAgentEnvironmentUserSessionEntityType resource name string.
     *
     * @param {string} project
     * @param {string} environment
     * @param {string} user
     * @param {string} session
     * @param {string} entity_type
     * @returns {string} Resource name string.
     */
    projectAgentEnvironmentUserSessionEntityTypePath(project: string, environment: string, user: string, session: string, entityType: string): string;
    /**
     * Parse the project from ProjectAgentEnvironmentUserSessionEntityType resource.
     *
     * @param {string} projectAgentEnvironmentUserSessionEntityTypeName
     *   A fully-qualified path representing project_agent_environment_user_session_entity_type resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectAgentEnvironmentUserSessionEntityTypeName(projectAgentEnvironmentUserSessionEntityTypeName: string): string | number;
    /**
     * Parse the environment from ProjectAgentEnvironmentUserSessionEntityType resource.
     *
     * @param {string} projectAgentEnvironmentUserSessionEntityTypeName
     *   A fully-qualified path representing project_agent_environment_user_session_entity_type resource.
     * @returns {string} A string representing the environment.
     */
    matchEnvironmentFromProjectAgentEnvironmentUserSessionEntityTypeName(projectAgentEnvironmentUserSessionEntityTypeName: string): string | number;
    /**
     * Parse the user from ProjectAgentEnvironmentUserSessionEntityType resource.
     *
     * @param {string} projectAgentEnvironmentUserSessionEntityTypeName
     *   A fully-qualified path representing project_agent_environment_user_session_entity_type resource.
     * @returns {string} A string representing the user.
     */
    matchUserFromProjectAgentEnvironmentUserSessionEntityTypeName(projectAgentEnvironmentUserSessionEntityTypeName: string): string | number;
    /**
     * Parse the session from ProjectAgentEnvironmentUserSessionEntityType resource.
     *
     * @param {string} projectAgentEnvironmentUserSessionEntityTypeName
     *   A fully-qualified path representing project_agent_environment_user_session_entity_type resource.
     * @returns {string} A string representing the session.
     */
    matchSessionFromProjectAgentEnvironmentUserSessionEntityTypeName(projectAgentEnvironmentUserSessionEntityTypeName: string): string | number;
    /**
     * Parse the entity_type from ProjectAgentEnvironmentUserSessionEntityType resource.
     *
     * @param {string} projectAgentEnvironmentUserSessionEntityTypeName
     *   A fully-qualified path representing project_agent_environment_user_session_entity_type resource.
     * @returns {string} A string representing the entity_type.
     */
    matchEntityTypeFromProjectAgentEnvironmentUserSessionEntityTypeName(projectAgentEnvironmentUserSessionEntityTypeName: string): string | number;
    /**
     * Return a fully-qualified projectAgentFulfillment resource name string.
     *
     * @param {string} project
     * @returns {string} Resource name string.
     */
    projectAgentFulfillmentPath(project: string): string;
    /**
     * Parse the project from ProjectAgentFulfillment resource.
     *
     * @param {string} projectAgentFulfillmentName
     *   A fully-qualified path representing project_agent_fulfillment resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectAgentFulfillmentName(projectAgentFulfillmentName: string): string | number;
    /**
     * Return a fully-qualified projectAgentIntent resource name string.
     *
     * @param {string} project
     * @param {string} intent
     * @returns {string} Resource name string.
     */
    projectAgentIntentPath(project: string, intent: string): string;
    /**
     * Parse the project from ProjectAgentIntent resource.
     *
     * @param {string} projectAgentIntentName
     *   A fully-qualified path representing project_agent_intent resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectAgentIntentName(projectAgentIntentName: string): string | number;
    /**
     * Parse the intent from ProjectAgentIntent resource.
     *
     * @param {string} projectAgentIntentName
     *   A fully-qualified path representing project_agent_intent resource.
     * @returns {string} A string representing the intent.
     */
    matchIntentFromProjectAgentIntentName(projectAgentIntentName: string): string | number;
    /**
     * Return a fully-qualified projectAgentSessionContext resource name string.
     *
     * @param {string} project
     * @param {string} session
     * @param {string} context
     * @returns {string} Resource name string.
     */
    projectAgentSessionContextPath(project: string, session: string, context: string): string;
    /**
     * Parse the project from ProjectAgentSessionContext resource.
     *
     * @param {string} projectAgentSessionContextName
     *   A fully-qualified path representing project_agent_session_context resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectAgentSessionContextName(projectAgentSessionContextName: string): string | number;
    /**
     * Parse the session from ProjectAgentSessionContext resource.
     *
     * @param {string} projectAgentSessionContextName
     *   A fully-qualified path representing project_agent_session_context resource.
     * @returns {string} A string representing the session.
     */
    matchSessionFromProjectAgentSessionContextName(projectAgentSessionContextName: string): string | number;
    /**
     * Parse the context from ProjectAgentSessionContext resource.
     *
     * @param {string} projectAgentSessionContextName
     *   A fully-qualified path representing project_agent_session_context resource.
     * @returns {string} A string representing the context.
     */
    matchContextFromProjectAgentSessionContextName(projectAgentSessionContextName: string): string | number;
    /**
     * Return a fully-qualified projectAgentSessionEntityType resource name string.
     *
     * @param {string} project
     * @param {string} session
     * @param {string} entity_type
     * @returns {string} Resource name string.
     */
    projectAgentSessionEntityTypePath(project: string, session: string, entityType: string): string;
    /**
     * Parse the project from ProjectAgentSessionEntityType resource.
     *
     * @param {string} projectAgentSessionEntityTypeName
     *   A fully-qualified path representing project_agent_session_entity_type resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectAgentSessionEntityTypeName(projectAgentSessionEntityTypeName: string): string | number;
    /**
     * Parse the session from ProjectAgentSessionEntityType resource.
     *
     * @param {string} projectAgentSessionEntityTypeName
     *   A fully-qualified path representing project_agent_session_entity_type resource.
     * @returns {string} A string representing the session.
     */
    matchSessionFromProjectAgentSessionEntityTypeName(projectAgentSessionEntityTypeName: string): string | number;
    /**
     * Parse the entity_type from ProjectAgentSessionEntityType resource.
     *
     * @param {string} projectAgentSessionEntityTypeName
     *   A fully-qualified path representing project_agent_session_entity_type resource.
     * @returns {string} A string representing the entity_type.
     */
    matchEntityTypeFromProjectAgentSessionEntityTypeName(projectAgentSessionEntityTypeName: string): string | number;
    /**
     * Return a fully-qualified projectAgentVersion resource name string.
     *
     * @param {string} project
     * @param {string} version
     * @returns {string} Resource name string.
     */
    projectAgentVersionPath(project: string, version: string): string;
    /**
     * Parse the project from ProjectAgentVersion resource.
     *
     * @param {string} projectAgentVersionName
     *   A fully-qualified path representing project_agent_version resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectAgentVersionName(projectAgentVersionName: string): string | number;
    /**
     * Parse the version from ProjectAgentVersion resource.
     *
     * @param {string} projectAgentVersionName
     *   A fully-qualified path representing project_agent_version resource.
     * @returns {string} A string representing the version.
     */
    matchVersionFromProjectAgentVersionName(projectAgentVersionName: string): string | number;
    /**
     * Return a fully-qualified projectAnswerRecord resource name string.
     *
     * @param {string} project
     * @param {string} answer_record
     * @returns {string} Resource name string.
     */
    projectAnswerRecordPath(project: string, answerRecord: string): string;
    /**
     * Parse the project from ProjectAnswerRecord resource.
     *
     * @param {string} projectAnswerRecordName
     *   A fully-qualified path representing project_answer_record resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectAnswerRecordName(projectAnswerRecordName: string): string | number;
    /**
     * Parse the answer_record from ProjectAnswerRecord resource.
     *
     * @param {string} projectAnswerRecordName
     *   A fully-qualified path representing project_answer_record resource.
     * @returns {string} A string representing the answer_record.
     */
    matchAnswerRecordFromProjectAnswerRecordName(projectAnswerRecordName: string): string | number;
    /**
     * Return a fully-qualified projectConversation resource name string.
     *
     * @param {string} project
     * @param {string} conversation
     * @returns {string} Resource name string.
     */
    projectConversationPath(project: string, conversation: string): string;
    /**
     * Parse the project from ProjectConversation resource.
     *
     * @param {string} projectConversationName
     *   A fully-qualified path representing project_conversation resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectConversationName(projectConversationName: string): string | number;
    /**
     * Parse the conversation from ProjectConversation resource.
     *
     * @param {string} projectConversationName
     *   A fully-qualified path representing project_conversation resource.
     * @returns {string} A string representing the conversation.
     */
    matchConversationFromProjectConversationName(projectConversationName: string): string | number;
    /**
     * Return a fully-qualified projectConversationMessage resource name string.
     *
     * @param {string} project
     * @param {string} conversation
     * @param {string} message
     * @returns {string} Resource name string.
     */
    projectConversationMessagePath(project: string, conversation: string, message: string): string;
    /**
     * Parse the project from ProjectConversationMessage resource.
     *
     * @param {string} projectConversationMessageName
     *   A fully-qualified path representing project_conversation_message resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectConversationMessageName(projectConversationMessageName: string): string | number;
    /**
     * Parse the conversation from ProjectConversationMessage resource.
     *
     * @param {string} projectConversationMessageName
     *   A fully-qualified path representing project_conversation_message resource.
     * @returns {string} A string representing the conversation.
     */
    matchConversationFromProjectConversationMessageName(projectConversationMessageName: string): string | number;
    /**
     * Parse the message from ProjectConversationMessage resource.
     *
     * @param {string} projectConversationMessageName
     *   A fully-qualified path representing project_conversation_message resource.
     * @returns {string} A string representing the message.
     */
    matchMessageFromProjectConversationMessageName(projectConversationMessageName: string): string | number;
    /**
     * Return a fully-qualified projectConversationModel resource name string.
     *
     * @param {string} project
     * @param {string} conversation_model
     * @returns {string} Resource name string.
     */
    projectConversationModelPath(project: string, conversationModel: string): string;
    /**
     * Parse the project from ProjectConversationModel resource.
     *
     * @param {string} projectConversationModelName
     *   A fully-qualified path representing project_conversation_model resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectConversationModelName(projectConversationModelName: string): string | number;
    /**
     * Parse the conversation_model from ProjectConversationModel resource.
     *
     * @param {string} projectConversationModelName
     *   A fully-qualified path representing project_conversation_model resource.
     * @returns {string} A string representing the conversation_model.
     */
    matchConversationModelFromProjectConversationModelName(projectConversationModelName: string): string | number;
    /**
     * Return a fully-qualified projectConversationModelEvaluation resource name string.
     *
     * @param {string} project
     * @param {string} conversation_model
     * @param {string} evaluation
     * @returns {string} Resource name string.
     */
    projectConversationModelEvaluationPath(project: string, conversationModel: string, evaluation: string): string;
    /**
     * Parse the project from ProjectConversationModelEvaluation resource.
     *
     * @param {string} projectConversationModelEvaluationName
     *   A fully-qualified path representing project_conversation_model_evaluation resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectConversationModelEvaluationName(projectConversationModelEvaluationName: string): string | number;
    /**
     * Parse the conversation_model from ProjectConversationModelEvaluation resource.
     *
     * @param {string} projectConversationModelEvaluationName
     *   A fully-qualified path representing project_conversation_model_evaluation resource.
     * @returns {string} A string representing the conversation_model.
     */
    matchConversationModelFromProjectConversationModelEvaluationName(projectConversationModelEvaluationName: string): string | number;
    /**
     * Parse the evaluation from ProjectConversationModelEvaluation resource.
     *
     * @param {string} projectConversationModelEvaluationName
     *   A fully-qualified path representing project_conversation_model_evaluation resource.
     * @returns {string} A string representing the evaluation.
     */
    matchEvaluationFromProjectConversationModelEvaluationName(projectConversationModelEvaluationName: string): string | number;
    /**
     * Return a fully-qualified projectConversationParticipant resource name string.
     *
     * @param {string} project
     * @param {string} conversation
     * @param {string} participant
     * @returns {string} Resource name string.
     */
    projectConversationParticipantPath(project: string, conversation: string, participant: string): string;
    /**
     * Parse the project from ProjectConversationParticipant resource.
     *
     * @param {string} projectConversationParticipantName
     *   A fully-qualified path representing project_conversation_participant resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectConversationParticipantName(projectConversationParticipantName: string): string | number;
    /**
     * Parse the conversation from ProjectConversationParticipant resource.
     *
     * @param {string} projectConversationParticipantName
     *   A fully-qualified path representing project_conversation_participant resource.
     * @returns {string} A string representing the conversation.
     */
    matchConversationFromProjectConversationParticipantName(projectConversationParticipantName: string): string | number;
    /**
     * Parse the participant from ProjectConversationParticipant resource.
     *
     * @param {string} projectConversationParticipantName
     *   A fully-qualified path representing project_conversation_participant resource.
     * @returns {string} A string representing the participant.
     */
    matchParticipantFromProjectConversationParticipantName(projectConversationParticipantName: string): string | number;
    /**
     * Return a fully-qualified projectConversationProfile resource name string.
     *
     * @param {string} project
     * @param {string} conversation_profile
     * @returns {string} Resource name string.
     */
    projectConversationProfilePath(project: string, conversationProfile: string): string;
    /**
     * Parse the project from ProjectConversationProfile resource.
     *
     * @param {string} projectConversationProfileName
     *   A fully-qualified path representing project_conversation_profile resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectConversationProfileName(projectConversationProfileName: string): string | number;
    /**
     * Parse the conversation_profile from ProjectConversationProfile resource.
     *
     * @param {string} projectConversationProfileName
     *   A fully-qualified path representing project_conversation_profile resource.
     * @returns {string} A string representing the conversation_profile.
     */
    matchConversationProfileFromProjectConversationProfileName(projectConversationProfileName: string): string | number;
    /**
     * Return a fully-qualified projectKnowledgeBase resource name string.
     *
     * @param {string} project
     * @param {string} knowledge_base
     * @returns {string} Resource name string.
     */
    projectKnowledgeBasePath(project: string, knowledgeBase: string): string;
    /**
     * Parse the project from ProjectKnowledgeBase resource.
     *
     * @param {string} projectKnowledgeBaseName
     *   A fully-qualified path representing project_knowledge_base resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectKnowledgeBaseName(projectKnowledgeBaseName: string): string | number;
    /**
     * Parse the knowledge_base from ProjectKnowledgeBase resource.
     *
     * @param {string} projectKnowledgeBaseName
     *   A fully-qualified path representing project_knowledge_base resource.
     * @returns {string} A string representing the knowledge_base.
     */
    matchKnowledgeBaseFromProjectKnowledgeBaseName(projectKnowledgeBaseName: string): string | number;
    /**
     * Return a fully-qualified projectKnowledgeBaseDocument resource name string.
     *
     * @param {string} project
     * @param {string} knowledge_base
     * @param {string} document
     * @returns {string} Resource name string.
     */
    projectKnowledgeBaseDocumentPath(project: string, knowledgeBase: string, document: string): string;
    /**
     * Parse the project from ProjectKnowledgeBaseDocument resource.
     *
     * @param {string} projectKnowledgeBaseDocumentName
     *   A fully-qualified path representing project_knowledge_base_document resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectKnowledgeBaseDocumentName(projectKnowledgeBaseDocumentName: string): string | number;
    /**
     * Parse the knowledge_base from ProjectKnowledgeBaseDocument resource.
     *
     * @param {string} projectKnowledgeBaseDocumentName
     *   A fully-qualified path representing project_knowledge_base_document resource.
     * @returns {string} A string representing the knowledge_base.
     */
    matchKnowledgeBaseFromProjectKnowledgeBaseDocumentName(projectKnowledgeBaseDocumentName: string): string | number;
    /**
     * Parse the document from ProjectKnowledgeBaseDocument resource.
     *
     * @param {string} projectKnowledgeBaseDocumentName
     *   A fully-qualified path representing project_knowledge_base_document resource.
     * @returns {string} A string representing the document.
     */
    matchDocumentFromProjectKnowledgeBaseDocumentName(projectKnowledgeBaseDocumentName: string): string | number;
    /**
     * Return a fully-qualified projectLocationAgent resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @returns {string} Resource name string.
     */
    projectLocationAgentPath(project: string, location: string): string;
    /**
     * Parse the project from ProjectLocationAgent resource.
     *
     * @param {string} projectLocationAgentName
     *   A fully-qualified path representing project_location_agent resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationAgentName(projectLocationAgentName: string): string | number;
    /**
     * Parse the location from ProjectLocationAgent resource.
     *
     * @param {string} projectLocationAgentName
     *   A fully-qualified path representing project_location_agent resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationAgentName(projectLocationAgentName: string): string | number;
    /**
     * Return a fully-qualified projectLocationAgentEntityType resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} entity_type
     * @returns {string} Resource name string.
     */
    projectLocationAgentEntityTypePath(project: string, location: string, entityType: string): string;
    /**
     * Parse the project from ProjectLocationAgentEntityType resource.
     *
     * @param {string} projectLocationAgentEntityTypeName
     *   A fully-qualified path representing project_location_agent_entity_type resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationAgentEntityTypeName(projectLocationAgentEntityTypeName: string): string | number;
    /**
     * Parse the location from ProjectLocationAgentEntityType resource.
     *
     * @param {string} projectLocationAgentEntityTypeName
     *   A fully-qualified path representing project_location_agent_entity_type resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationAgentEntityTypeName(projectLocationAgentEntityTypeName: string): string | number;
    /**
     * Parse the entity_type from ProjectLocationAgentEntityType resource.
     *
     * @param {string} projectLocationAgentEntityTypeName
     *   A fully-qualified path representing project_location_agent_entity_type resource.
     * @returns {string} A string representing the entity_type.
     */
    matchEntityTypeFromProjectLocationAgentEntityTypeName(projectLocationAgentEntityTypeName: string): string | number;
    /**
     * Return a fully-qualified projectLocationAgentEnvironment resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} environment
     * @returns {string} Resource name string.
     */
    projectLocationAgentEnvironmentPath(project: string, location: string, environment: string): string;
    /**
     * Parse the project from ProjectLocationAgentEnvironment resource.
     *
     * @param {string} projectLocationAgentEnvironmentName
     *   A fully-qualified path representing project_location_agent_environment resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationAgentEnvironmentName(projectLocationAgentEnvironmentName: string): string | number;
    /**
     * Parse the location from ProjectLocationAgentEnvironment resource.
     *
     * @param {string} projectLocationAgentEnvironmentName
     *   A fully-qualified path representing project_location_agent_environment resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationAgentEnvironmentName(projectLocationAgentEnvironmentName: string): string | number;
    /**
     * Parse the environment from ProjectLocationAgentEnvironment resource.
     *
     * @param {string} projectLocationAgentEnvironmentName
     *   A fully-qualified path representing project_location_agent_environment resource.
     * @returns {string} A string representing the environment.
     */
    matchEnvironmentFromProjectLocationAgentEnvironmentName(projectLocationAgentEnvironmentName: string): string | number;
    /**
     * Return a fully-qualified projectLocationAgentEnvironmentUserSessionContext resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} environment
     * @param {string} user
     * @param {string} session
     * @param {string} context
     * @returns {string} Resource name string.
     */
    projectLocationAgentEnvironmentUserSessionContextPath(project: string, location: string, environment: string, user: string, session: string, context: string): string;
    /**
     * Parse the project from ProjectLocationAgentEnvironmentUserSessionContext resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionContextName
     *   A fully-qualified path representing project_location_agent_environment_user_session_context resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationAgentEnvironmentUserSessionContextName(projectLocationAgentEnvironmentUserSessionContextName: string): string | number;
    /**
     * Parse the location from ProjectLocationAgentEnvironmentUserSessionContext resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionContextName
     *   A fully-qualified path representing project_location_agent_environment_user_session_context resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationAgentEnvironmentUserSessionContextName(projectLocationAgentEnvironmentUserSessionContextName: string): string | number;
    /**
     * Parse the environment from ProjectLocationAgentEnvironmentUserSessionContext resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionContextName
     *   A fully-qualified path representing project_location_agent_environment_user_session_context resource.
     * @returns {string} A string representing the environment.
     */
    matchEnvironmentFromProjectLocationAgentEnvironmentUserSessionContextName(projectLocationAgentEnvironmentUserSessionContextName: string): string | number;
    /**
     * Parse the user from ProjectLocationAgentEnvironmentUserSessionContext resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionContextName
     *   A fully-qualified path representing project_location_agent_environment_user_session_context resource.
     * @returns {string} A string representing the user.
     */
    matchUserFromProjectLocationAgentEnvironmentUserSessionContextName(projectLocationAgentEnvironmentUserSessionContextName: string): string | number;
    /**
     * Parse the session from ProjectLocationAgentEnvironmentUserSessionContext resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionContextName
     *   A fully-qualified path representing project_location_agent_environment_user_session_context resource.
     * @returns {string} A string representing the session.
     */
    matchSessionFromProjectLocationAgentEnvironmentUserSessionContextName(projectLocationAgentEnvironmentUserSessionContextName: string): string | number;
    /**
     * Parse the context from ProjectLocationAgentEnvironmentUserSessionContext resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionContextName
     *   A fully-qualified path representing project_location_agent_environment_user_session_context resource.
     * @returns {string} A string representing the context.
     */
    matchContextFromProjectLocationAgentEnvironmentUserSessionContextName(projectLocationAgentEnvironmentUserSessionContextName: string): string | number;
    /**
     * Return a fully-qualified projectLocationAgentEnvironmentUserSessionEntityType resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} environment
     * @param {string} user
     * @param {string} session
     * @param {string} entity_type
     * @returns {string} Resource name string.
     */
    projectLocationAgentEnvironmentUserSessionEntityTypePath(project: string, location: string, environment: string, user: string, session: string, entityType: string): string;
    /**
     * Parse the project from ProjectLocationAgentEnvironmentUserSessionEntityType resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionEntityTypeName
     *   A fully-qualified path representing project_location_agent_environment_user_session_entity_type resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationAgentEnvironmentUserSessionEntityTypeName(projectLocationAgentEnvironmentUserSessionEntityTypeName: string): string | number;
    /**
     * Parse the location from ProjectLocationAgentEnvironmentUserSessionEntityType resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionEntityTypeName
     *   A fully-qualified path representing project_location_agent_environment_user_session_entity_type resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationAgentEnvironmentUserSessionEntityTypeName(projectLocationAgentEnvironmentUserSessionEntityTypeName: string): string | number;
    /**
     * Parse the environment from ProjectLocationAgentEnvironmentUserSessionEntityType resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionEntityTypeName
     *   A fully-qualified path representing project_location_agent_environment_user_session_entity_type resource.
     * @returns {string} A string representing the environment.
     */
    matchEnvironmentFromProjectLocationAgentEnvironmentUserSessionEntityTypeName(projectLocationAgentEnvironmentUserSessionEntityTypeName: string): string | number;
    /**
     * Parse the user from ProjectLocationAgentEnvironmentUserSessionEntityType resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionEntityTypeName
     *   A fully-qualified path representing project_location_agent_environment_user_session_entity_type resource.
     * @returns {string} A string representing the user.
     */
    matchUserFromProjectLocationAgentEnvironmentUserSessionEntityTypeName(projectLocationAgentEnvironmentUserSessionEntityTypeName: string): string | number;
    /**
     * Parse the session from ProjectLocationAgentEnvironmentUserSessionEntityType resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionEntityTypeName
     *   A fully-qualified path representing project_location_agent_environment_user_session_entity_type resource.
     * @returns {string} A string representing the session.
     */
    matchSessionFromProjectLocationAgentEnvironmentUserSessionEntityTypeName(projectLocationAgentEnvironmentUserSessionEntityTypeName: string): string | number;
    /**
     * Parse the entity_type from ProjectLocationAgentEnvironmentUserSessionEntityType resource.
     *
     * @param {string} projectLocationAgentEnvironmentUserSessionEntityTypeName
     *   A fully-qualified path representing project_location_agent_environment_user_session_entity_type resource.
     * @returns {string} A string representing the entity_type.
     */
    matchEntityTypeFromProjectLocationAgentEnvironmentUserSessionEntityTypeName(projectLocationAgentEnvironmentUserSessionEntityTypeName: string): string | number;
    /**
     * Return a fully-qualified projectLocationAgentFulfillment resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @returns {string} Resource name string.
     */
    projectLocationAgentFulfillmentPath(project: string, location: string): string;
    /**
     * Parse the project from ProjectLocationAgentFulfillment resource.
     *
     * @param {string} projectLocationAgentFulfillmentName
     *   A fully-qualified path representing project_location_agent_fulfillment resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationAgentFulfillmentName(projectLocationAgentFulfillmentName: string): string | number;
    /**
     * Parse the location from ProjectLocationAgentFulfillment resource.
     *
     * @param {string} projectLocationAgentFulfillmentName
     *   A fully-qualified path representing project_location_agent_fulfillment resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationAgentFulfillmentName(projectLocationAgentFulfillmentName: string): string | number;
    /**
     * Return a fully-qualified projectLocationAgentIntent resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} intent
     * @returns {string} Resource name string.
     */
    projectLocationAgentIntentPath(project: string, location: string, intent: string): string;
    /**
     * Parse the project from ProjectLocationAgentIntent resource.
     *
     * @param {string} projectLocationAgentIntentName
     *   A fully-qualified path representing project_location_agent_intent resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationAgentIntentName(projectLocationAgentIntentName: string): string | number;
    /**
     * Parse the location from ProjectLocationAgentIntent resource.
     *
     * @param {string} projectLocationAgentIntentName
     *   A fully-qualified path representing project_location_agent_intent resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationAgentIntentName(projectLocationAgentIntentName: string): string | number;
    /**
     * Parse the intent from ProjectLocationAgentIntent resource.
     *
     * @param {string} projectLocationAgentIntentName
     *   A fully-qualified path representing project_location_agent_intent resource.
     * @returns {string} A string representing the intent.
     */
    matchIntentFromProjectLocationAgentIntentName(projectLocationAgentIntentName: string): string | number;
    /**
     * Return a fully-qualified projectLocationAgentSessionContext resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} session
     * @param {string} context
     * @returns {string} Resource name string.
     */
    projectLocationAgentSessionContextPath(project: string, location: string, session: string, context: string): string;
    /**
     * Parse the project from ProjectLocationAgentSessionContext resource.
     *
     * @param {string} projectLocationAgentSessionContextName
     *   A fully-qualified path representing project_location_agent_session_context resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationAgentSessionContextName(projectLocationAgentSessionContextName: string): string | number;
    /**
     * Parse the location from ProjectLocationAgentSessionContext resource.
     *
     * @param {string} projectLocationAgentSessionContextName
     *   A fully-qualified path representing project_location_agent_session_context resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationAgentSessionContextName(projectLocationAgentSessionContextName: string): string | number;
    /**
     * Parse the session from ProjectLocationAgentSessionContext resource.
     *
     * @param {string} projectLocationAgentSessionContextName
     *   A fully-qualified path representing project_location_agent_session_context resource.
     * @returns {string} A string representing the session.
     */
    matchSessionFromProjectLocationAgentSessionContextName(projectLocationAgentSessionContextName: string): string | number;
    /**
     * Parse the context from ProjectLocationAgentSessionContext resource.
     *
     * @param {string} projectLocationAgentSessionContextName
     *   A fully-qualified path representing project_location_agent_session_context resource.
     * @returns {string} A string representing the context.
     */
    matchContextFromProjectLocationAgentSessionContextName(projectLocationAgentSessionContextName: string): string | number;
    /**
     * Return a fully-qualified projectLocationAgentSessionEntityType resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} session
     * @param {string} entity_type
     * @returns {string} Resource name string.
     */
    projectLocationAgentSessionEntityTypePath(project: string, location: string, session: string, entityType: string): string;
    /**
     * Parse the project from ProjectLocationAgentSessionEntityType resource.
     *
     * @param {string} projectLocationAgentSessionEntityTypeName
     *   A fully-qualified path representing project_location_agent_session_entity_type resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationAgentSessionEntityTypeName(projectLocationAgentSessionEntityTypeName: string): string | number;
    /**
     * Parse the location from ProjectLocationAgentSessionEntityType resource.
     *
     * @param {string} projectLocationAgentSessionEntityTypeName
     *   A fully-qualified path representing project_location_agent_session_entity_type resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationAgentSessionEntityTypeName(projectLocationAgentSessionEntityTypeName: string): string | number;
    /**
     * Parse the session from ProjectLocationAgentSessionEntityType resource.
     *
     * @param {string} projectLocationAgentSessionEntityTypeName
     *   A fully-qualified path representing project_location_agent_session_entity_type resource.
     * @returns {string} A string representing the session.
     */
    matchSessionFromProjectLocationAgentSessionEntityTypeName(projectLocationAgentSessionEntityTypeName: string): string | number;
    /**
     * Parse the entity_type from ProjectLocationAgentSessionEntityType resource.
     *
     * @param {string} projectLocationAgentSessionEntityTypeName
     *   A fully-qualified path representing project_location_agent_session_entity_type resource.
     * @returns {string} A string representing the entity_type.
     */
    matchEntityTypeFromProjectLocationAgentSessionEntityTypeName(projectLocationAgentSessionEntityTypeName: string): string | number;
    /**
     * Return a fully-qualified projectLocationAgentVersion resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} version
     * @returns {string} Resource name string.
     */
    projectLocationAgentVersionPath(project: string, location: string, version: string): string;
    /**
     * Parse the project from ProjectLocationAgentVersion resource.
     *
     * @param {string} projectLocationAgentVersionName
     *   A fully-qualified path representing project_location_agent_version resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationAgentVersionName(projectLocationAgentVersionName: string): string | number;
    /**
     * Parse the location from ProjectLocationAgentVersion resource.
     *
     * @param {string} projectLocationAgentVersionName
     *   A fully-qualified path representing project_location_agent_version resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationAgentVersionName(projectLocationAgentVersionName: string): string | number;
    /**
     * Parse the version from ProjectLocationAgentVersion resource.
     *
     * @param {string} projectLocationAgentVersionName
     *   A fully-qualified path representing project_location_agent_version resource.
     * @returns {string} A string representing the version.
     */
    matchVersionFromProjectLocationAgentVersionName(projectLocationAgentVersionName: string): string | number;
    /**
     * Return a fully-qualified projectLocationAnswerRecord resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} answer_record
     * @returns {string} Resource name string.
     */
    projectLocationAnswerRecordPath(project: string, location: string, answerRecord: string): string;
    /**
     * Parse the project from ProjectLocationAnswerRecord resource.
     *
     * @param {string} projectLocationAnswerRecordName
     *   A fully-qualified path representing project_location_answer_record resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationAnswerRecordName(projectLocationAnswerRecordName: string): string | number;
    /**
     * Parse the location from ProjectLocationAnswerRecord resource.
     *
     * @param {string} projectLocationAnswerRecordName
     *   A fully-qualified path representing project_location_answer_record resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationAnswerRecordName(projectLocationAnswerRecordName: string): string | number;
    /**
     * Parse the answer_record from ProjectLocationAnswerRecord resource.
     *
     * @param {string} projectLocationAnswerRecordName
     *   A fully-qualified path representing project_location_answer_record resource.
     * @returns {string} A string representing the answer_record.
     */
    matchAnswerRecordFromProjectLocationAnswerRecordName(projectLocationAnswerRecordName: string): string | number;
    /**
     * Return a fully-qualified projectLocationConversation resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} conversation
     * @returns {string} Resource name string.
     */
    projectLocationConversationPath(project: string, location: string, conversation: string): string;
    /**
     * Parse the project from ProjectLocationConversation resource.
     *
     * @param {string} projectLocationConversationName
     *   A fully-qualified path representing project_location_conversation resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationConversationName(projectLocationConversationName: string): string | number;
    /**
     * Parse the location from ProjectLocationConversation resource.
     *
     * @param {string} projectLocationConversationName
     *   A fully-qualified path representing project_location_conversation resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationConversationName(projectLocationConversationName: string): string | number;
    /**
     * Parse the conversation from ProjectLocationConversation resource.
     *
     * @param {string} projectLocationConversationName
     *   A fully-qualified path representing project_location_conversation resource.
     * @returns {string} A string representing the conversation.
     */
    matchConversationFromProjectLocationConversationName(projectLocationConversationName: string): string | number;
    /**
     * Return a fully-qualified projectLocationConversationMessage resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} conversation
     * @param {string} message
     * @returns {string} Resource name string.
     */
    projectLocationConversationMessagePath(project: string, location: string, conversation: string, message: string): string;
    /**
     * Parse the project from ProjectLocationConversationMessage resource.
     *
     * @param {string} projectLocationConversationMessageName
     *   A fully-qualified path representing project_location_conversation_message resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationConversationMessageName(projectLocationConversationMessageName: string): string | number;
    /**
     * Parse the location from ProjectLocationConversationMessage resource.
     *
     * @param {string} projectLocationConversationMessageName
     *   A fully-qualified path representing project_location_conversation_message resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationConversationMessageName(projectLocationConversationMessageName: string): string | number;
    /**
     * Parse the conversation from ProjectLocationConversationMessage resource.
     *
     * @param {string} projectLocationConversationMessageName
     *   A fully-qualified path representing project_location_conversation_message resource.
     * @returns {string} A string representing the conversation.
     */
    matchConversationFromProjectLocationConversationMessageName(projectLocationConversationMessageName: string): string | number;
    /**
     * Parse the message from ProjectLocationConversationMessage resource.
     *
     * @param {string} projectLocationConversationMessageName
     *   A fully-qualified path representing project_location_conversation_message resource.
     * @returns {string} A string representing the message.
     */
    matchMessageFromProjectLocationConversationMessageName(projectLocationConversationMessageName: string): string | number;
    /**
     * Return a fully-qualified projectLocationConversationModel resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} conversation_model
     * @returns {string} Resource name string.
     */
    projectLocationConversationModelPath(project: string, location: string, conversationModel: string): string;
    /**
     * Parse the project from ProjectLocationConversationModel resource.
     *
     * @param {string} projectLocationConversationModelName
     *   A fully-qualified path representing project_location_conversation_model resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationConversationModelName(projectLocationConversationModelName: string): string | number;
    /**
     * Parse the location from ProjectLocationConversationModel resource.
     *
     * @param {string} projectLocationConversationModelName
     *   A fully-qualified path representing project_location_conversation_model resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationConversationModelName(projectLocationConversationModelName: string): string | number;
    /**
     * Parse the conversation_model from ProjectLocationConversationModel resource.
     *
     * @param {string} projectLocationConversationModelName
     *   A fully-qualified path representing project_location_conversation_model resource.
     * @returns {string} A string representing the conversation_model.
     */
    matchConversationModelFromProjectLocationConversationModelName(projectLocationConversationModelName: string): string | number;
    /**
     * Return a fully-qualified projectLocationConversationModelEvaluation resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} conversation_model
     * @param {string} evaluation
     * @returns {string} Resource name string.
     */
    projectLocationConversationModelEvaluationPath(project: string, location: string, conversationModel: string, evaluation: string): string;
    /**
     * Parse the project from ProjectLocationConversationModelEvaluation resource.
     *
     * @param {string} projectLocationConversationModelEvaluationName
     *   A fully-qualified path representing project_location_conversation_model_evaluation resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationConversationModelEvaluationName(projectLocationConversationModelEvaluationName: string): string | number;
    /**
     * Parse the location from ProjectLocationConversationModelEvaluation resource.
     *
     * @param {string} projectLocationConversationModelEvaluationName
     *   A fully-qualified path representing project_location_conversation_model_evaluation resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationConversationModelEvaluationName(projectLocationConversationModelEvaluationName: string): string | number;
    /**
     * Parse the conversation_model from ProjectLocationConversationModelEvaluation resource.
     *
     * @param {string} projectLocationConversationModelEvaluationName
     *   A fully-qualified path representing project_location_conversation_model_evaluation resource.
     * @returns {string} A string representing the conversation_model.
     */
    matchConversationModelFromProjectLocationConversationModelEvaluationName(projectLocationConversationModelEvaluationName: string): string | number;
    /**
     * Parse the evaluation from ProjectLocationConversationModelEvaluation resource.
     *
     * @param {string} projectLocationConversationModelEvaluationName
     *   A fully-qualified path representing project_location_conversation_model_evaluation resource.
     * @returns {string} A string representing the evaluation.
     */
    matchEvaluationFromProjectLocationConversationModelEvaluationName(projectLocationConversationModelEvaluationName: string): string | number;
    /**
     * Return a fully-qualified projectLocationConversationParticipant resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} conversation
     * @param {string} participant
     * @returns {string} Resource name string.
     */
    projectLocationConversationParticipantPath(project: string, location: string, conversation: string, participant: string): string;
    /**
     * Parse the project from ProjectLocationConversationParticipant resource.
     *
     * @param {string} projectLocationConversationParticipantName
     *   A fully-qualified path representing project_location_conversation_participant resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationConversationParticipantName(projectLocationConversationParticipantName: string): string | number;
    /**
     * Parse the location from ProjectLocationConversationParticipant resource.
     *
     * @param {string} projectLocationConversationParticipantName
     *   A fully-qualified path representing project_location_conversation_participant resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationConversationParticipantName(projectLocationConversationParticipantName: string): string | number;
    /**
     * Parse the conversation from ProjectLocationConversationParticipant resource.
     *
     * @param {string} projectLocationConversationParticipantName
     *   A fully-qualified path representing project_location_conversation_participant resource.
     * @returns {string} A string representing the conversation.
     */
    matchConversationFromProjectLocationConversationParticipantName(projectLocationConversationParticipantName: string): string | number;
    /**
     * Parse the participant from ProjectLocationConversationParticipant resource.
     *
     * @param {string} projectLocationConversationParticipantName
     *   A fully-qualified path representing project_location_conversation_participant resource.
     * @returns {string} A string representing the participant.
     */
    matchParticipantFromProjectLocationConversationParticipantName(projectLocationConversationParticipantName: string): string | number;
    /**
     * Return a fully-qualified projectLocationConversationProfile resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} conversation_profile
     * @returns {string} Resource name string.
     */
    projectLocationConversationProfilePath(project: string, location: string, conversationProfile: string): string;
    /**
     * Parse the project from ProjectLocationConversationProfile resource.
     *
     * @param {string} projectLocationConversationProfileName
     *   A fully-qualified path representing project_location_conversation_profile resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationConversationProfileName(projectLocationConversationProfileName: string): string | number;
    /**
     * Parse the location from ProjectLocationConversationProfile resource.
     *
     * @param {string} projectLocationConversationProfileName
     *   A fully-qualified path representing project_location_conversation_profile resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationConversationProfileName(projectLocationConversationProfileName: string): string | number;
    /**
     * Parse the conversation_profile from ProjectLocationConversationProfile resource.
     *
     * @param {string} projectLocationConversationProfileName
     *   A fully-qualified path representing project_location_conversation_profile resource.
     * @returns {string} A string representing the conversation_profile.
     */
    matchConversationProfileFromProjectLocationConversationProfileName(projectLocationConversationProfileName: string): string | number;
    /**
     * Return a fully-qualified projectLocationKnowledgeBase resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} knowledge_base
     * @returns {string} Resource name string.
     */
    projectLocationKnowledgeBasePath(project: string, location: string, knowledgeBase: string): string;
    /**
     * Parse the project from ProjectLocationKnowledgeBase resource.
     *
     * @param {string} projectLocationKnowledgeBaseName
     *   A fully-qualified path representing project_location_knowledge_base resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationKnowledgeBaseName(projectLocationKnowledgeBaseName: string): string | number;
    /**
     * Parse the location from ProjectLocationKnowledgeBase resource.
     *
     * @param {string} projectLocationKnowledgeBaseName
     *   A fully-qualified path representing project_location_knowledge_base resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationKnowledgeBaseName(projectLocationKnowledgeBaseName: string): string | number;
    /**
     * Parse the knowledge_base from ProjectLocationKnowledgeBase resource.
     *
     * @param {string} projectLocationKnowledgeBaseName
     *   A fully-qualified path representing project_location_knowledge_base resource.
     * @returns {string} A string representing the knowledge_base.
     */
    matchKnowledgeBaseFromProjectLocationKnowledgeBaseName(projectLocationKnowledgeBaseName: string): string | number;
    /**
     * Return a fully-qualified projectLocationKnowledgeBaseDocument resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} knowledge_base
     * @param {string} document
     * @returns {string} Resource name string.
     */
    projectLocationKnowledgeBaseDocumentPath(project: string, location: string, knowledgeBase: string, document: string): string;
    /**
     * Parse the project from ProjectLocationKnowledgeBaseDocument resource.
     *
     * @param {string} projectLocationKnowledgeBaseDocumentName
     *   A fully-qualified path representing project_location_knowledge_base_document resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationKnowledgeBaseDocumentName(projectLocationKnowledgeBaseDocumentName: string): string | number;
    /**
     * Parse the location from ProjectLocationKnowledgeBaseDocument resource.
     *
     * @param {string} projectLocationKnowledgeBaseDocumentName
     *   A fully-qualified path representing project_location_knowledge_base_document resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationKnowledgeBaseDocumentName(projectLocationKnowledgeBaseDocumentName: string): string | number;
    /**
     * Parse the knowledge_base from ProjectLocationKnowledgeBaseDocument resource.
     *
     * @param {string} projectLocationKnowledgeBaseDocumentName
     *   A fully-qualified path representing project_location_knowledge_base_document resource.
     * @returns {string} A string representing the knowledge_base.
     */
    matchKnowledgeBaseFromProjectLocationKnowledgeBaseDocumentName(projectLocationKnowledgeBaseDocumentName: string): string | number;
    /**
     * Parse the document from ProjectLocationKnowledgeBaseDocument resource.
     *
     * @param {string} projectLocationKnowledgeBaseDocumentName
     *   A fully-qualified path representing project_location_knowledge_base_document resource.
     * @returns {string} A string representing the document.
     */
    matchDocumentFromProjectLocationKnowledgeBaseDocumentName(projectLocationKnowledgeBaseDocumentName: string): string | number;
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close(): Promise<void>;
}
