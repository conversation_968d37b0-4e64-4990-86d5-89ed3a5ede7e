{"interfaces": {"google.cloud.dialogflow.v2.Agents": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"], "unavailable": ["UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"GetAgent": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "SetAgent": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "DeleteAgent": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "SearchAgents": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "TrainAgent": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "ExportAgent": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "ImportAgent": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "RestoreAgent": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "GetValidationResult": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}}}}}