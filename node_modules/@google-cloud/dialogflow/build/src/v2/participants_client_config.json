{"interfaces": {"google.cloud.dialogflow.v2.Participants": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"], "unavailable": ["UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"CreateParticipant": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "GetParticipant": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "ListParticipants": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "UpdateParticipant": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "AnalyzeContent": {"timeout_millis": 220000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "StreamingAnalyzeContent": {"timeout_millis": 220000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "SuggestArticles": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "SuggestFaqAnswers": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "SuggestSmartReplies": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}, "SuggestKnowledgeAssist": {"timeout_millis": 60000, "retry_codes_name": "unavailable", "retry_params_name": "default"}}}}}