"use strict";
// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// ** This file is automatically generated by synthtool. **
// ** https://github.com/googleapis/synthtool **
// ** All changes to this file may be overwritten. **
Object.defineProperty(exports, "__esModule", { value: true });
exports.protos = exports.VersionsClient = exports.SessionsClient = exports.SessionEntityTypesClient = exports.ParticipantsClient = exports.KnowledgeBasesClient = exports.IntentsClient = exports.GeneratorsClient = exports.FulfillmentsClient = exports.EnvironmentsClient = exports.EntityTypesClient = exports.EncryptionSpecServiceClient = exports.DocumentsClient = exports.ConversationsClient = exports.ConversationProfilesClient = exports.ConversationModelsClient = exports.ConversationDatasetsClient = exports.ContextsClient = exports.AnswerRecordsClient = exports.AgentsClient = exports.v2beta1 = exports.v2 = void 0;
const v2 = require("./v2");
exports.v2 = v2;
const v2beta1 = require("./v2beta1");
exports.v2beta1 = v2beta1;
const AgentsClient = v2.AgentsClient;
exports.AgentsClient = AgentsClient;
const AnswerRecordsClient = v2.AnswerRecordsClient;
exports.AnswerRecordsClient = AnswerRecordsClient;
const ContextsClient = v2.ContextsClient;
exports.ContextsClient = ContextsClient;
const ConversationDatasetsClient = v2.ConversationDatasetsClient;
exports.ConversationDatasetsClient = ConversationDatasetsClient;
const ConversationModelsClient = v2.ConversationModelsClient;
exports.ConversationModelsClient = ConversationModelsClient;
const ConversationProfilesClient = v2.ConversationProfilesClient;
exports.ConversationProfilesClient = ConversationProfilesClient;
const ConversationsClient = v2.ConversationsClient;
exports.ConversationsClient = ConversationsClient;
const DocumentsClient = v2.DocumentsClient;
exports.DocumentsClient = DocumentsClient;
const EncryptionSpecServiceClient = v2.EncryptionSpecServiceClient;
exports.EncryptionSpecServiceClient = EncryptionSpecServiceClient;
const EntityTypesClient = v2.EntityTypesClient;
exports.EntityTypesClient = EntityTypesClient;
const EnvironmentsClient = v2.EnvironmentsClient;
exports.EnvironmentsClient = EnvironmentsClient;
const FulfillmentsClient = v2.FulfillmentsClient;
exports.FulfillmentsClient = FulfillmentsClient;
const GeneratorsClient = v2.GeneratorsClient;
exports.GeneratorsClient = GeneratorsClient;
const IntentsClient = v2.IntentsClient;
exports.IntentsClient = IntentsClient;
const KnowledgeBasesClient = v2.KnowledgeBasesClient;
exports.KnowledgeBasesClient = KnowledgeBasesClient;
const ParticipantsClient = v2.ParticipantsClient;
exports.ParticipantsClient = ParticipantsClient;
const SessionEntityTypesClient = v2.SessionEntityTypesClient;
exports.SessionEntityTypesClient = SessionEntityTypesClient;
const SessionsClient = v2.SessionsClient;
exports.SessionsClient = SessionsClient;
const VersionsClient = v2.VersionsClient;
exports.VersionsClient = VersionsClient;
exports.default = {
    v2,
    v2beta1,
    AgentsClient,
    AnswerRecordsClient,
    ContextsClient,
    ConversationDatasetsClient,
    ConversationModelsClient,
    ConversationProfilesClient,
    ConversationsClient,
    DocumentsClient,
    EncryptionSpecServiceClient,
    EntityTypesClient,
    EnvironmentsClient,
    FulfillmentsClient,
    GeneratorsClient,
    IntentsClient,
    KnowledgeBasesClient,
    ParticipantsClient,
    SessionEntityTypesClient,
    SessionsClient,
    VersionsClient,
};
const protos = require("../protos/protos");
exports.protos = protos;
//# sourceMappingURL=index.js.map