{"name": "@google-cloud/dialogflow", "description": "Dialogflow API client for Node.js", "version": "6.14.0", "license": "Apache-2.0", "author": "Google LLC", "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "directory": "packages/google-cloud-dialogflow", "url": "https://github.com/googleapis/google-cloud-node.git"}, "main": "build/src/index.js", "files": ["build/protos", "build/src", "!build/src/**/*.map"], "keywords": ["google apis client", "google api client", "google apis", "google api", "google", "google cloud platform", "google cloud", "cloud", "google dialogflow", "dialogflow", "Dialogflow API"], "scripts": {"clean": "gts clean", "compile": "tsc -p . && cp -r protos build/", "compile-protos": "compileProtos src", "docs": "jsdoc -c .jsdoc.js", "predocs-test": "npm run docs", "docs-test": "linkinator docs", "fix": "gts fix", "lint": "gts check", "prepare": "npm run compile", "prelint": "cd samples; npm link ../; npm i", "postpack": "minifyProtoJson", "samples-test": "cd samples/ && npm link ../ && npm i && npm test", "system-test": "c8 mocha build/system-test", "test": "c8 mocha build/test/*_v2.js && c8 mocha build/test/*_v2beta1.js"}, "dependencies": {"google-gax": "^4.0.3"}, "devDependencies": {"@types/mocha": "^9.0.0", "@types/node": "^22.0.0", "@types/sinon": "^17.0.0", "c8": "^9.0.0", "codecov": "^3.6.5", "gapic-tools": "^0.4.0", "gts": "^5.0.0", "jsdoc": "^4.0.0", "jsdoc-fresh": "^3.0.0", "jsdoc-region-tag": "^3.0.0", "linkinator": "4.1.2", "long": "^5.2.3", "mocha": "^9.2.2", "pack-n-play": "^2.0.0", "sinon": "^18.0.0", "typescript": "^5.1.6"}, "homepage": "https://github.com/googleapis/google-cloud-node/tree/main/packages/google-cloud-dialogflow"}