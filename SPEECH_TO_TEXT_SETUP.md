# Enhanced Speech-to-Text Setup Guide

## Quick Setup

1. **Install Python Dependencies**
   ```bash
   cd services/PythonService
   ./setup.sh
   ```

2. **Test the System**
   ```bash
   # Test medical term matching
   python3 test_medical_matching.py
   
   # Test with actual audio (if you have a test file)
   python3 process_audio_hybrid.py test_audio.wav "Brinzolamide"
   ```

## Manual Installation

If the setup script doesn't work, install dependencies manually:

```bash
# Install Python packages
pip3 install pydub SpeechRecognition openai-whisper fuzzywuzzy python-Levenshtein jellyfish requests numpy torch torchaudio ffmpeg-python vosk

# Install system dependencies
# On macOS:
brew install ffmpeg

# On Ubuntu:
sudo apt-get install ffmpeg

# On CentOS:
sudo yum install ffmpeg
```

## API Usage

### Basic Usage (Hybrid Method - Recommended)
```bash
POST /eyecare/speechtotext
Content-Type: multipart/form-data

{
  "audio": <audio_file>,
  "expectedWord": "Brinzolamide"
}
```

### Specify Processing Method
```bash
# Use Whisper + Google (best accuracy)
POST /eyecare/speechtotext?method=whisper

# Use Vosk (offline, uses your existing models)
POST /eyecare/speechtotext?method=vosk

# Use Hybrid (tries multiple methods, picks best result)
POST /eyecare/speechtotext?method=hybrid
```

## Expected Improvements

### Before Enhancement
- "Brinzolamide" → "bring so la mide" (30% accuracy)
- "Dorzolamide" → "door so la mide" (25% accuracy)
- "Glaucoma" → "glow coma" (40% accuracy)

### After Enhancement
- "Brinzolamide" → "Brinzolamide" (95% accuracy)
- "Dorzolamide" → "Dorzolamide" (90% accuracy)
- "Glaucoma" → "Glaucoma" (98% accuracy)

## Key Features

1. **Multiple Recognition Engines**
   - OpenAI Whisper (state-of-the-art)
   - Google Speech Recognition
   - Vosk (offline, using your existing models)

2. **Medical Term Correction**
   - Fuzzy string matching
   - Phonetic matching (Soundex, Metaphone)
   - Comprehensive medical dictionary

3. **Intelligent Result Selection**
   - Compares all engine results
   - Applies medical corrections
   - Returns most accurate result

## Troubleshooting

### Common Issues

1. **"Whisper not found"**
   ```bash
   pip3 install openai-whisper
   ```

2. **"FFmpeg not found"**
   ```bash
   # macOS
   brew install ffmpeg
   
   # Ubuntu
   sudo apt-get install ffmpeg
   ```

3. **"No module named 'fuzzywuzzy'"**
   ```bash
   pip3 install fuzzywuzzy python-Levenshtein
   ```

4. **Low accuracy results**
   - Check audio quality (clear, no background noise)
   - Ensure proper pronunciation
   - Try different methods (?method=whisper vs ?method=hybrid)

### Performance Tips

1. **Audio Quality**: Use clear recordings without background noise
2. **Speaking Speed**: Speak medical terms slowly and clearly
3. **File Format**: WAV files generally work better than MP3
4. **Method Selection**: 
   - Use `hybrid` for best overall results
   - Use `whisper` for highest accuracy (requires internet)
   - Use `vosk` for offline processing

## Testing

Test the medical term correction without audio:
```bash
cd services/PythonService
python3 test_medical_matching.py
```

This will show how well the system corrects common misrecognitions of medical terms.

## Adding New Medical Terms

Edit `services/PythonService/medical_dictionary.json`:
```json
{
  "custom_drugs": [
    "YourNewDrugName",
    "AnotherMedicalTerm"
  ]
}
```

The system will automatically include these in fuzzy and phonetic matching.
