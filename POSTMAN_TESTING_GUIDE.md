# 🚀 Postman Testing Guide - Enhanced Speech-to-Text API

## 📥 **Import the Postman Collection**

1. **Download the Collection File:**
   - File: `Medical_Dictionary_Speech_to_Text_API.postman_collection.json`

2. **Import into Postman:**
   - Open Postman
   - Click "Import" button
   - Drag and drop the JSON file or click "Upload Files"
   - Select the collection file
   - Click "Import"

---

## ⚙️ **Setup Environment Variables**

1. **Create Environment:**
   - Click "Environments" in Postman
   - Click "+" to create new environment
   - Name it "Medical Dictionary API"

2. **Set Variables:**
   ```
   Variable: base_url
   Initial Value: http://localhost:3000
   Current Value: http://localhost:3000
   ```

3. **Select Environment:**
   - Choose "Medical Dictionary API" from environment dropdown

---

## 🎵 **Prepare Test Audio Files**

### Option 1: Use Generated Test Files
If you have the test files we created:
```bash
# Navigate to the Python service directory
cd services/PythonService

# List available test files
ls test_*_converted.wav

# Available files:
# - test_brinzolamide_converted.wav
# - test_resync_converted.wav  
# - test_travisight_converted.wav
# - test_veldrop_converted.wav
```

### Option 2: Create Your Own Audio Files
Record yourself saying the medical terms clearly:
- **Brinzolamide** (BRIN-zo-la-mide)
- **resync** (re-SYNC)
- **Travisight** (TRAVIS-sight)
- **Veldrop** (VEL-drop)

**Recommended format:** WAV, 16kHz, mono

---

## 🧪 **Testing Steps**

### 1. **Start Your Server**
```bash
# Make sure your Node.js server is running
npm start
# or
node server.js
```

### 2. **Test Basic Functionality**

**Request:** `Speech to Text - Hybrid Method (Recommended)`

1. Select the request from collection
2. In Body → form-data:
   - **audio**: Upload your audio file
   - **expectedWord**: Enter the medical term (e.g., "Brinzolamide")
3. Click "Send"

**Expected Response:**
```json
{
    "expectedText": "Brinzolamide",
    "transcribedText": "Brinzolamide", 
    "accuracy": "95.00",
    "method": "hybrid"
}
```

### 3. **Test Different Methods**

#### **Hybrid Method (Best Overall)**
- URL: `POST /eyecare/speechtotext?method=hybrid`
- Uses multiple engines + medical correction
- **Recommended for production**

#### **Whisper Method (Highest Accuracy)**
- URL: `POST /eyecare/speechtotext?method=whisper`
- Uses OpenAI Whisper model
- Requires internet connection

#### **Vosk Method (Offline)**
- URL: `POST /eyecare/speechtotext?method=vosk`
- Uses your local Vosk models
- Works offline

### 4. **Test Specific Medical Terms**

Use the pre-configured requests:

#### **Test - Brinzolamide**
- Tests the original problematic term
- Expected improvement: 30% → 90%+ accuracy

#### **Test - resync**
- Tests medical device synchronization term
- Should handle "re sync" → "resync"

#### **Test - Travisight**
- Tests medical imaging system term
- Should handle "travis sight" → "Travisight"

#### **Test - Veldrop**
- Tests medical eye drop term
- Should handle "vel drop" → "Veldrop"

---

## 📊 **Understanding Response**

### **Successful Response:**
```json
{
    "expectedText": "Brinzolamide",
    "transcribedText": "Brinzolamide",
    "accuracy": "95.00",
    "method": "hybrid"
}
```

### **Response Fields:**
- **expectedText**: What you expected to be recognized
- **transcribedText**: What the system actually transcribed
- **accuracy**: Similarity percentage between expected and transcribed
- **method**: Which processing method was used

### **Error Response:**
```json
{
    "error": "No file uploaded or expected word missing"
}
```

---

## 🎯 **Testing Scenarios**

### **Scenario 1: Perfect Recognition**
- Upload clear audio of "Brinzolamide"
- Expected: High accuracy (90%+)
- Tests: Basic functionality

### **Scenario 2: Poor Audio Quality**
- Upload unclear/noisy audio
- Expected: Lower base accuracy, but medical correction should help
- Tests: Correction algorithms

### **Scenario 3: Mispronunciation**
- Record "bring-so-la-mide" (syllable separated)
- Expected: System corrects to "Brinzolamide"
- Tests: Medical term correction

### **Scenario 4: Different Methods**
- Same audio file with different method parameters
- Expected: Different accuracy levels
- Tests: Engine comparison

---

## 🔧 **Troubleshooting**

### **Common Issues:**

1. **"No file uploaded"**
   - Ensure audio file is selected in form-data
   - Check file format (WAV/MP3 supported)

2. **"Python script execution failed"**
   - Ensure Python dependencies are installed
   - Run: `cd services/PythonService && pip3 install -r requirements.txt`

3. **"Audio conversion failed"**
   - Ensure FFmpeg is installed
   - Check audio file isn't corrupted

4. **Low accuracy results**
   - Try different method parameter (?method=whisper)
   - Ensure clear pronunciation
   - Check if term is in medical dictionary

### **Debug Steps:**
1. Check server logs for detailed error messages
2. Test with known good audio file first
3. Verify all Python dependencies are installed
4. Try different processing methods

---

## 📈 **Expected Results**

### **Before Enhancement:**
- Brinzolamide: ~30% accuracy
- Medical terms often misrecognized

### **After Enhancement:**
- Brinzolamide: 85-95% accuracy
- Medical terms correctly identified
- Syllable-separated words corrected

### **Success Indicators:**
- ✅ Accuracy > 80% for medical terms
- ✅ Correct medical term in transcribedText
- ✅ Method field shows processing type
- ✅ No error messages

---

## 🎉 **Ready to Test!**

Your enhanced speech-to-text API is ready for comprehensive testing. The Postman collection includes all the requests you need to test the medical terms we've been working on.

**Start with the "Hybrid Method" request for best results!**
