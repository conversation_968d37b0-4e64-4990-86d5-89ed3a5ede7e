# 🔧 cURL Testing Examples

If you prefer command line testing, here are cURL examples for your enhanced speech-to-text API:

## 🚀 **Basic Testing Commands**

### **1. Test with Hybrid Method (Recommended)**
```bash
curl -X POST "http://localhost:3000/eyecare/speechtotext?method=hybrid" \
  -F "audio=@/path/to/your/audio/file.wav" \
  -F "expectedWord=Brinzolamide"
```

### **2. Test with Whisper Method**
```bash
curl -X POST "http://localhost:3000/eyecare/speechtotext?method=whisper" \
  -F "audio=@/path/to/your/audio/file.wav" \
  -F "expectedWord=Travisight"
```

### **3. Test with Vosk Method (Offline)**
```bash
curl -X POST "http://localhost:3000/eyecare/speechtotext?method=vosk" \
  -F "audio=@/path/to/your/audio/file.wav" \
  -F "expectedWord=Veldrop"
```

## 🧪 **Test Specific Medical Terms**

### **Test Brinzolamide**
```bash
# Using test file we created
curl -X POST "http://localhost:3000/eyecare/speechtotext?method=hybrid" \
  -F "audio=@services/PythonService/test_brinzolamide_converted.wav" \
  -F "expectedWord=Brinzolamide"
```

### **Test resync**
```bash
curl -X POST "http://localhost:3000/eyecare/speechtotext?method=hybrid" \
  -F "audio=@services/PythonService/test_resync_converted.wav" \
  -F "expectedWord=resync"
```

### **Test Travisight**
```bash
curl -X POST "http://localhost:3000/eyecare/speechtotext?method=hybrid" \
  -F "audio=@services/PythonService/test_travisight_converted.wav" \
  -F "expectedWord=Travisight"
```

### **Test Veldrop**
```bash
curl -X POST "http://localhost:3000/eyecare/speechtotext?method=hybrid" \
  -F "audio=@services/PythonService/test_veldrop_converted.wav" \
  -F "expectedWord=Veldrop"
```

## 📊 **Expected Response Format**

### **Successful Response:**
```json
{
    "expectedText": "Brinzolamide",
    "transcribedText": "Brinzolamide",
    "accuracy": "95.00",
    "method": "hybrid"
}
```

### **Error Response:**
```json
{
    "error": "No file uploaded or expected word missing"
}
```

## 🔧 **Testing Script**

Create a test script to run multiple tests:

```bash
#!/bin/bash
# test_api.sh

BASE_URL="http://localhost:3000/eyecare/speechtotext"
AUDIO_DIR="services/PythonService"

echo "🧪 Testing Enhanced Speech-to-Text API"
echo "======================================"

# Test medical terms
declare -a terms=("brinzolamide" "resync" "travisight" "veldrop")
declare -a expected=("Brinzolamide" "resync" "Travisight" "Veldrop")

for i in "${!terms[@]}"; do
    term="${terms[$i]}"
    expect="${expected[$i]}"
    audio_file="${AUDIO_DIR}/test_${term}_converted.wav"
    
    echo ""
    echo "🔍 Testing: $expect"
    echo "Audio file: $audio_file"
    
    if [ -f "$audio_file" ]; then
        response=$(curl -s -X POST "${BASE_URL}?method=hybrid" \
            -F "audio=@${audio_file}" \
            -F "expectedWord=${expect}")
        
        echo "Response: $response"
    else
        echo "❌ Audio file not found: $audio_file"
    fi
done

echo ""
echo "✅ Testing complete!"
```

Make it executable and run:
```bash
chmod +x test_api.sh
./test_api.sh
```

## 🎯 **Quick Test Commands**

### **Test if server is running:**
```bash
curl -X GET "http://localhost:3000/health" || echo "Server not running"
```

### **Test with different audio formats:**
```bash
# WAV file
curl -X POST "http://localhost:3000/eyecare/speechtotext?method=hybrid" \
  -F "audio=@test.wav" \
  -F "expectedWord=Brinzolamide"

# MP3 file  
curl -X POST "http://localhost:3000/eyecare/speechtotext?method=hybrid" \
  -F "audio=@test.mp3" \
  -F "expectedWord=Brinzolamide"
```

### **Test error handling:**
```bash
# Missing audio file
curl -X POST "http://localhost:3000/eyecare/speechtotext?method=hybrid" \
  -F "expectedWord=Brinzolamide"

# Missing expected word
curl -X POST "http://localhost:3000/eyecare/speechtotext?method=hybrid" \
  -F "audio=@test.wav"
```

## 📝 **Notes**

- Replace `/path/to/your/audio/file.wav` with actual file path
- Ensure your server is running on `http://localhost:3000`
- Use `-v` flag with curl for verbose output if debugging
- Audio files should be WAV or MP3 format
- The API expects `multipart/form-data` content type
