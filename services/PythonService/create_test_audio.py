#!/usr/bin/env python3
"""
Create test audio files for testing speech-to-text accuracy
"""

import os
import subprocess
import sys

def create_audio_with_say(text, filename):
    """Create audio file using macOS 'say' command"""
    try:
        # Use macOS built-in text-to-speech
        cmd = ['say', '-o', filename, '--data-format=LEF32@22050', text]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ Created audio file: {filename}")
            return True
        else:
            print(f"❌ Error creating audio: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def create_audio_with_espeak(text, filename):
    """Create audio file using espeak (cross-platform)"""
    try:
        # Convert to WAV using espeak
        cmd = ['espeak', '-w', filename, text]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ Created audio file: {filename}")
            return True
        else:
            print(f"❌ Error creating audio: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ espeak not available: {e}")
        return False

def create_test_audio_files():
    """Create test audio files for medical terms"""
    
    test_cases = [
        ("Brinzolamide", "test_brinzolamide.wav"),
        ("Dorzolamide", "test_dorzolamide.wav"),
        ("Timolol", "test_timolol.wav"),
        ("Glaucoma", "test_glaucoma.wav"),
        ("The patient needs Brinzolamide eye drops", "test_sentence_brinzolamide.wav")
    ]
    
    print("Creating test audio files...")
    print("=" * 50)
    
    success_count = 0
    
    for text, filename in test_cases:
        print(f"\nCreating audio for: '{text}'")
        
        # Try macOS 'say' command first
        if create_audio_with_say(text, filename):
            success_count += 1
        # Fallback to espeak
        elif create_audio_with_espeak(text, filename):
            success_count += 1
        else:
            print(f"❌ Failed to create audio for: {text}")
    
    print(f"\n✅ Successfully created {success_count}/{len(test_cases)} audio files")
    
    if success_count > 0:
        print("\nTo test the speech-to-text system:")
        for text, filename in test_cases[:success_count]:
            if os.path.exists(filename):
                word = text.split()[0] if len(text.split()) == 1 else text.split()[-3]  # Get main medical term
                print(f"python3 process_audio_hybrid.py {filename} \"{word}\"")

if __name__ == "__main__":
    create_test_audio_files()
