#!/usr/bin/env python3
"""
Test script to demonstrate medical term correction capabilities
"""

import json
import os
from fuzzywuzzy import fuzz, process
import jellyfish

def load_medical_dictionary():
    """Load medical terms dictionary"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        dict_path = os.path.join(script_dir, 'medical_dictionary.json')
        
        with open(dict_path, 'r') as f:
            medical_dict = json.load(f)
        
        medical_terms = []
        for category, terms in medical_dict.items():
            medical_terms.extend(terms)
        
        return medical_terms
    except Exception as e:
        print(f"Error loading dictionary: {e}")
        return []

def test_fuzzy_matching():
    """Test fuzzy matching with common misrecognitions"""
    medical_terms = load_medical_dictionary()
    
    # Common misrecognitions of medical terms
    test_cases = [
        ("bring so la mide", "Brinzolamide"),
        ("door so la mide", "Dorzolamide"),
        ("tim o lol", "Timolol"),
        ("la tan o prost", "Latanoprost"),
        ("bi mat o prost", "Bimatoprost"),
        ("pre dni so lone", "Prednisolone"),
        ("dex a meth a zone", "Dexamethasone"),
        ("glow coma", "Glaucoma"),
        ("cat a ract", "Cataract"),
        ("ret i na", "Retina"),
        ("cor nea", "Cornea"),
        ("mac u la", "Macula")
    ]
    
    print("Testing Medical Term Correction")
    print("=" * 50)
    
    for misrecognized, expected in test_cases:
        # Test fuzzy matching
        best_match = process.extractOne(misrecognized, medical_terms, scorer=fuzz.ratio)
        
        # Test phonetic matching
        soundex_match = None
        best_soundex_score = 0
        
        misrecognized_soundex = jellyfish.soundex(misrecognized)
        for term in medical_terms:
            if jellyfish.soundex(term) == misrecognized_soundex:
                jaro_score = jellyfish.jaro_winkler_similarity(misrecognized.lower(), term.lower())
                if jaro_score > best_soundex_score:
                    best_soundex_score = jaro_score
                    soundex_match = term
        
        print(f"\nInput: '{misrecognized}' → Expected: '{expected}'")
        
        if best_match:
            print(f"  Fuzzy Match: '{best_match[0]}' (Score: {best_match[1]})")
            fuzzy_correct = best_match[0] == expected
        else:
            print("  Fuzzy Match: No match found")
            fuzzy_correct = False
        
        if soundex_match:
            print(f"  Phonetic Match: '{soundex_match}' (Score: {best_soundex_score:.2f})")
            phonetic_correct = soundex_match == expected
        else:
            print("  Phonetic Match: No match found")
            phonetic_correct = False
        
        # Overall result
        if fuzzy_correct or phonetic_correct:
            print("  ✅ CORRECTED SUCCESSFULLY")
        else:
            print("  ❌ NOT CORRECTED")

def test_sentence_correction():
    """Test correction in full sentences"""
    medical_terms = load_medical_dictionary()

    test_sentences = [
        ("The patient was prescribed bring so la mide eye drops", "Brinzolamide"),
        ("We need to check the glow coma pressure", "Glaucoma"),
        ("The cat a ract surgery went well", "Cataract"),
        ("Apply pre dni so lone twice daily", "Prednisolone")
    ]

    print("\n\nTesting Sentence Correction")
    print("=" * 50)

    for sentence, expected_term in test_sentences:
        words = sentence.split()
        corrected_words = []

        # Try to match multi-word medical terms first
        sentence_lower = sentence.lower()

        # Check for multi-word patterns
        multi_word_patterns = [
            ("bring so la mide", "Brinzolamide"),
            ("door so la mide", "Dorzolamide"),
            ("glow coma", "Glaucoma"),
            ("cat a ract", "Cataract"),
            ("pre dni so lone", "Prednisolone"),
            ("dex a meth a zone", "Dexamethasone")
        ]

        corrected_sentence = sentence
        for pattern, replacement in multi_word_patterns:
            if pattern in sentence_lower:
                corrected_sentence = corrected_sentence.replace(pattern, replacement)
                corrected_sentence = corrected_sentence.replace(pattern.title(), replacement)

        # If no multi-word pattern matched, try individual words
        if corrected_sentence == sentence:
            for word in words:
                clean_word = word.lower().replace('.', '').replace(',', '')
                if len(clean_word) > 2:
                    best_match = process.extractOne(clean_word, medical_terms, scorer=fuzz.ratio)

                    if best_match and best_match[1] >= 70:
                        corrected_words.append(best_match[0])
                    else:
                        corrected_words.append(word)
                else:
                    corrected_words.append(word)

            corrected_sentence = ' '.join(corrected_words)

        print(f"\nOriginal: {sentence}")
        print(f"Corrected: {corrected_sentence}")

        if expected_term in corrected_sentence:
            print("✅ CORRECTION SUCCESSFUL")
        else:
            print("❌ CORRECTION FAILED")

if __name__ == "__main__":
    test_fuzzy_matching()
    test_sentence_correction()
    
    print("\n\nTo test with actual audio files:")
    print("python3 process_audio_hybrid.py <audio_file> <expected_text>")
