import sys
import os
import json
import re
from pydub import AudioSegment
import speech_recognition as sr
from difflib import SequenceMatcher
import whisper
from fuzzywuzzy import fuzz, process
import jellyfish
import requests

def get_accuracy(expected_text, actual_text):
    return SequenceMatcher(None, expected_text.lower(), actual_text.lower()).ratio() * 100

def load_medical_dictionary():
    """Load medical terms dictionary for better matching"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        dict_path = os.path.join(script_dir, 'medical_dictionary.json')

        with open(dict_path, 'r') as f:
            medical_dict = json.load(f)

        # Flatten all medical terms into a single list
        medical_terms = []
        for category, terms in medical_dict.items():
            medical_terms.extend(terms)

        return medical_terms
    except Exception as e:
        print(f"Warning: Could not load medical dictionary: {e}")
        # Fallback to basic list
        return [
            "Brinzolamide", "Dorzolamide", "Timolol", "Latanoprost", "Bimatoprost",
            "Travoprost", "<PERSON><PERSON>lu<PERSON>rost", "Brimonidine", "Apraclonidine", "Pilocarpine",
            "Carbachol", "Acetazolamide", "Methazolamide", "Mannitol", "Glycerin",
            "Prednisolone", "Dexamethasone", "Fluorometholone", "Loteprednol",
            "Rimexolone", "Cyclosporine", "Tacrolimus", "Ketorolac", "Diclofenac"
        ]

def phonetic_match(word, medical_terms):
    """Use phonetic matching to find similar sounding medical terms"""
    best_match = None
    best_score = 0

    word_soundex = jellyfish.soundex(word)
    word_metaphone = jellyfish.metaphone(word)

    for term in medical_terms:
        term_soundex = jellyfish.soundex(term)
        term_metaphone = jellyfish.metaphone(term)

        # Calculate phonetic similarity
        soundex_match = 1.0 if word_soundex == term_soundex else 0.0
        metaphone_match = 1.0 if word_metaphone == term_metaphone else 0.0

        # Also use Jaro-Winkler for string similarity
        jaro_score = jellyfish.jaro_winkler_similarity(word.lower(), term.lower())

        # Combined score
        combined_score = (soundex_match * 0.3 + metaphone_match * 0.3 + jaro_score * 0.4)

        if combined_score > best_score and combined_score > 0.6:
            best_score = combined_score
            best_match = term

    return best_match, best_score

def fuzzy_match_medical_terms(text, medical_terms, threshold=70):
    """Use fuzzy matching to correct medical terms in transcribed text"""
    words = text.split()
    corrected_words = []

    for word in words:
        # Clean the word
        clean_word = re.sub(r'[^\w]', '', word)

        if len(clean_word) < 3:
            corrected_words.append(word)
            continue

        # Try fuzzy matching
        best_match = process.extractOne(clean_word, medical_terms, scorer=fuzz.ratio)

        if best_match and best_match[1] >= threshold:
            corrected_words.append(best_match[0])
        else:
            # Try phonetic matching
            phonetic_match_result, phonetic_score = phonetic_match(clean_word, medical_terms)
            if phonetic_match_result and phonetic_score > 0.6:
                corrected_words.append(phonetic_match_result)
            else:
                corrected_words.append(word)

    return ' '.join(corrected_words)

def enhance_with_whisper(audio_file):
    """Use Whisper for more accurate transcription"""
    try:
        # Load Whisper model (using base model for balance of speed and accuracy)
        model = whisper.load_model("base")

        # Transcribe audio
        result = model.transcribe(audio_file)
        return result["text"].strip()
    except Exception as e:
        print(f"Whisper transcription failed: {e}")
        return None

def multi_engine_transcription(audio_file):
    """Use multiple transcription engines and combine results"""
    results = []

    # Method 1: Whisper
    whisper_result = enhance_with_whisper(audio_file)
    if whisper_result:
        results.append(("Whisper", whisper_result))

    # Method 2: Google Speech Recognition
    recognizer = sr.Recognizer()
    try:
        with sr.AudioFile(audio_file) as source:
            # Adjust for ambient noise
            recognizer.adjust_for_ambient_noise(source, duration=0.5)
            audio_data = recognizer.record(source)

            # Try Google
            try:
                google_result = recognizer.recognize_google(audio_data)
                results.append(("Google", google_result))
            except:
                pass

            # Try Google Cloud (if available)
            try:
                google_cloud_result = recognizer.recognize_google_cloud(audio_data)
                results.append(("Google Cloud", google_cloud_result))
            except:
                pass
    except Exception as e:
        print(f"Speech recognition error: {e}")

    return results

# Check arguments
if len(sys.argv) < 3:
    print("Usage: python process_audio.py <audio_file> <expected_text>")
    sys.exit(1)

audio_file = sys.argv[1]
expected_text = sys.argv[2]

# Load medical dictionary
medical_terms = load_medical_dictionary()

# Handle different audio formats
if audio_file.endswith('.mp3'):
    wav_file = audio_file.replace(".mp3", ".wav")
    try:
        audio = AudioSegment.from_file(audio_file, format="mp3")
        audio.export(wav_file, format="wav")
        audio_file = wav_file
    except Exception as e:
        print(f"Error converting MP3 to WAV: {e}")
        sys.exit(1)

# Perform multi-engine transcription
transcription_results = multi_engine_transcription(audio_file)

if not transcription_results:
    print("Expected: " + expected_text)
    print("Transcribed: Could not transcribe audio")
    print("Accuracy: 0.00")
    sys.exit(1)

# Process each result and find the best one
best_result = None
best_accuracy = 0
best_corrected = None

for engine, raw_transcription in transcription_results:
    # Apply medical term correction
    corrected_transcription = fuzzy_match_medical_terms(raw_transcription, medical_terms)

    # Calculate accuracy for both raw and corrected
    raw_accuracy = get_accuracy(expected_text, raw_transcription)
    corrected_accuracy = get_accuracy(expected_text, corrected_transcription)

    # Choose the better result
    if corrected_accuracy > raw_accuracy and corrected_accuracy > best_accuracy:
        best_accuracy = corrected_accuracy
        best_result = corrected_transcription
        best_corrected = True
    elif raw_accuracy > best_accuracy:
        best_accuracy = raw_accuracy
        best_result = raw_transcription
        best_corrected = False

# Print output for Node.js
print(f'Expected: {expected_text}')
print(f'Transcribed: {best_result}')
print(f'Accuracy: {best_accuracy:.2f}')

# Cleanup
if 'wav_file' in locals() and os.path.exists(wav_file):
    os.remove(wav_file)
