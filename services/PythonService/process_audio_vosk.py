import sys
import os
import json
import re
import wave
from pydub import AudioSegment
from difflib import SequenceMatcher
import vosk
from fuzzywuzzy import fuzz, process
import jellyfish

def get_accuracy(expected_text, actual_text):
    return SequenceMatcher(None, expected_text.lower(), actual_text.lower()).ratio() * 100

def load_medical_dictionary():
    """Load medical terms dictionary for better matching"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        dict_path = os.path.join(script_dir, 'medical_dictionary.json')
        
        with open(dict_path, 'r') as f:
            medical_dict = json.load(f)
        
        # Flatten all medical terms into a single list
        medical_terms = []
        for category, terms in medical_dict.items():
            medical_terms.extend(terms)
        
        return medical_terms
    except Exception as e:
        print(f"Warning: Could not load medical dictionary: {e}")
        # Fallback to basic list
        return [
            "Brinzolamide", "Dorzolamide", "Timolol", "Latanoprost", "Bimatoprost",
            "Travoprost", "Taflu<PERSON>rost", "Brimonidine", "Apraclonidine", "Pilocarpine"
        ]

def phonetic_match(word, medical_terms):
    """Use phonetic matching to find similar sounding medical terms"""
    best_match = None
    best_score = 0
    
    word_soundex = jellyfish.soundex(word)
    word_metaphone = jellyfish.metaphone(word)
    
    for term in medical_terms:
        term_soundex = jellyfish.soundex(term)
        term_metaphone = jellyfish.metaphone(term)
        
        # Calculate phonetic similarity
        soundex_match = 1.0 if word_soundex == term_soundex else 0.0
        metaphone_match = 1.0 if word_metaphone == term_metaphone else 0.0
        
        # Also use Jaro-Winkler for string similarity
        jaro_score = jellyfish.jaro_winkler_similarity(word.lower(), term.lower())
        
        # Combined score
        combined_score = (soundex_match * 0.3 + metaphone_match * 0.3 + jaro_score * 0.4)
        
        if combined_score > best_score and combined_score > 0.6:
            best_score = combined_score
            best_match = term
    
    return best_match, best_score

def fuzzy_match_medical_terms(text, medical_terms, threshold=70):
    """Use fuzzy matching to correct medical terms in transcribed text"""
    words = text.split()
    corrected_words = []
    
    for word in words:
        # Clean the word
        clean_word = re.sub(r'[^\w]', '', word)
        
        if len(clean_word) < 3:
            corrected_words.append(word)
            continue
        
        # Try fuzzy matching
        best_match = process.extractOne(clean_word, medical_terms, scorer=fuzz.ratio)
        
        if best_match and best_match[1] >= threshold:
            corrected_words.append(best_match[0])
        else:
            # Try phonetic matching
            phonetic_match_result, phonetic_score = phonetic_match(clean_word, medical_terms)
            if phonetic_match_result and phonetic_score > 0.6:
                corrected_words.append(phonetic_match_result)
            else:
                corrected_words.append(word)
    
    return ' '.join(corrected_words)

def transcribe_with_vosk(audio_file, model_path):
    """Use Vosk for offline speech recognition"""
    try:
        # Load Vosk model
        if not os.path.exists(model_path):
            print(f"Vosk model not found at {model_path}")
            return None
            
        model = vosk.Model(model_path)
        rec = vosk.KaldiRecognizer(model, 16000)
        
        # Open audio file
        wf = wave.open(audio_file, 'rb')
        
        if wf.getnchannels() != 1 or wf.getsampwidth() != 2 or wf.getcomptype() != "NONE":
            print("Audio file must be WAV format mono PCM.")
            return None
        
        results = []
        while True:
            data = wf.readframes(4000)
            if len(data) == 0:
                break
            if rec.AcceptWaveform(data):
                result = json.loads(rec.Result())
                if 'text' in result:
                    results.append(result['text'])
        
        # Get final result
        final_result = json.loads(rec.FinalResult())
        if 'text' in final_result:
            results.append(final_result['text'])
        
        wf.close()
        return ' '.join(results).strip()
        
    except Exception as e:
        print(f"Vosk transcription error: {e}")
        return None

def convert_audio_for_vosk(input_file):
    """Convert audio to format suitable for Vosk (16kHz, mono, WAV)"""
    try:
        # Load audio file
        audio = AudioSegment.from_file(input_file)
        
        # Convert to mono, 16kHz
        audio = audio.set_channels(1)
        audio = audio.set_frame_rate(16000)
        
        # Export as WAV
        output_file = input_file.replace(os.path.splitext(input_file)[1], "_vosk.wav")
        audio.export(output_file, format="wav")
        
        return output_file
    except Exception as e:
        print(f"Audio conversion error: {e}")
        return None

# Check arguments
if len(sys.argv) < 3:
    print("Usage: python process_audio_vosk.py <audio_file> <expected_text>")
    sys.exit(1)

audio_file = sys.argv[1]
expected_text = sys.argv[2]

# Load medical dictionary
medical_terms = load_medical_dictionary()

# Convert audio for Vosk
vosk_audio_file = convert_audio_for_vosk(audio_file)
if not vosk_audio_file:
    print("Expected: " + expected_text)
    print("Transcribed: Audio conversion failed")
    print("Accuracy: 0.00")
    sys.exit(1)

# Try different Vosk models
script_dir = os.path.dirname(os.path.abspath(__file__))
model_paths = [
    os.path.join(script_dir, "model", "vosk-model-en-us-0.22"),
    os.path.join(script_dir, "model", "vosk-model-small-en-us-0.15")
]

best_result = None
best_accuracy = 0

for model_path in model_paths:
    if os.path.exists(model_path):
        print(f"Trying model: {os.path.basename(model_path)}")
        
        # Transcribe with Vosk
        raw_transcription = transcribe_with_vosk(vosk_audio_file, model_path)
        
        if raw_transcription:
            # Apply medical term correction
            corrected_transcription = fuzzy_match_medical_terms(raw_transcription, medical_terms)
            
            # Calculate accuracy for both raw and corrected
            raw_accuracy = get_accuracy(expected_text, raw_transcription)
            corrected_accuracy = get_accuracy(expected_text, corrected_transcription)
            
            # Choose the better result
            if corrected_accuracy > raw_accuracy and corrected_accuracy > best_accuracy:
                best_accuracy = corrected_accuracy
                best_result = corrected_transcription
            elif raw_accuracy > best_accuracy:
                best_accuracy = raw_accuracy
                best_result = raw_transcription

if best_result is None:
    print("Expected: " + expected_text)
    print("Transcribed: Could not transcribe audio")
    print("Accuracy: 0.00")
else:
    print(f'Expected: {expected_text}')
    print(f'Transcribed: {best_result}')
    print(f'Accuracy: {best_accuracy:.2f}')

# Cleanup
if vosk_audio_file and os.path.exists(vosk_audio_file):
    os.remove(vosk_audio_file)
