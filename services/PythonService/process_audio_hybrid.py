import sys
import os
import json
import re
import subprocess
from pydub import AudioSegment
import speech_recognition as sr
from difflib import Se<PERSON><PERSON>atch<PERSON>
from fuzzywuzzy import fuzz, process
import jellyfish

def get_accuracy(expected_text, actual_text):
    return SequenceMatcher(None, expected_text.lower(), actual_text.lower()).ratio() * 100

def load_medical_dictionary():
    """Load medical terms dictionary for better matching"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        dict_path = os.path.join(script_dir, 'medical_dictionary.json')
        
        with open(dict_path, 'r') as f:
            medical_dict = json.load(f)
        
        # Flatten all medical terms into a single list
        medical_terms = []
        for category, terms in medical_dict.items():
            medical_terms.extend(terms)
        
        return medical_terms
    except Exception as e:
        print(f"Warning: Could not load medical dictionary: {e}")
        return []

def phonetic_match(word, medical_terms):
    """Use phonetic matching to find similar sounding medical terms"""
    best_match = None
    best_score = 0
    
    try:
        word_soundex = jellyfish.soundex(word)
        word_metaphone = jellyfish.metaphone(word)
        
        for term in medical_terms:
            term_soundex = jellyfish.soundex(term)
            term_metaphone = jellyfish.metaphone(term)
            
            # Calculate phonetic similarity
            soundex_match = 1.0 if word_soundex == term_soundex else 0.0
            metaphone_match = 1.0 if word_metaphone == term_metaphone else 0.0
            
            # Also use Jaro-Winkler for string similarity
            jaro_score = jellyfish.jaro_winkler_similarity(word.lower(), term.lower())
            
            # Combined score
            combined_score = (soundex_match * 0.3 + metaphone_match * 0.3 + jaro_score * 0.4)
            
            if combined_score > best_score and combined_score > 0.6:
                best_score = combined_score
                best_match = term
    except:
        pass
    
    return best_match, best_score

def fuzzy_match_medical_terms(text, medical_terms, threshold=60):
    """Use fuzzy matching to correct medical terms in transcribed text"""
    if not medical_terms:
        print("DEBUG: No medical terms loaded")
        return text

    print(f"DEBUG: Input text: '{text}'")
    print(f"DEBUG: Medical terms count: {len(medical_terms)}")

    # First try to match the whole phrase
    whole_phrase_match = process.extractOne(text, medical_terms, scorer=fuzz.ratio)
    if whole_phrase_match and whole_phrase_match[1] >= threshold:
        print(f"DEBUG: Whole phrase match: {whole_phrase_match}")
        return whole_phrase_match[0]

    # Try with cleaned text (remove punctuation)
    clean_text = re.sub(r'[^\w\s]', '', text)
    clean_phrase_match = process.extractOne(clean_text, medical_terms, scorer=fuzz.ratio)
    if clean_phrase_match and clean_phrase_match[1] >= threshold:
        print(f"DEBUG: Clean phrase match: {clean_phrase_match}")
        return clean_phrase_match[0]

    # Try token sort ratio (handles word order differences)
    token_match = process.extractOne(clean_text, medical_terms, scorer=fuzz.token_sort_ratio)
    if token_match and token_match[1] >= threshold:
        print(f"DEBUG: Token sort match: {token_match}")
        return token_match[0]

    # Try partial ratio (for partial matches)
    partial_match = process.extractOne(clean_text, medical_terms, scorer=fuzz.partial_ratio)
    if partial_match and partial_match[1] >= 70:  # Higher threshold for partial
        print(f"DEBUG: Partial match: {partial_match}")
        return partial_match[0]

    # If no good match found, try word-by-word
    words = text.split()
    corrected_words = []

    for word in words:
        # Clean the word
        clean_word = re.sub(r'[^\w]', '', word)

        if len(clean_word) < 3:
            corrected_words.append(word)
            continue

        # Try fuzzy matching
        try:
            best_match = process.extractOne(clean_word, medical_terms, scorer=fuzz.ratio)

            if best_match and best_match[1] >= threshold:
                print(f"DEBUG: Word '{clean_word}' matched to '{best_match[0]}' ({best_match[1]}%)")
                corrected_words.append(best_match[0])
            else:
                # Try phonetic matching
                phonetic_match_result, phonetic_score = phonetic_match(clean_word, medical_terms)
                if phonetic_match_result and phonetic_score > 0.6:
                    print(f"DEBUG: Phonetic match '{clean_word}' -> '{phonetic_match_result}' ({phonetic_score:.0%})")
                    corrected_words.append(phonetic_match_result)
                else:
                    corrected_words.append(word)
        except Exception as e:
            print(f"DEBUG: Error matching word '{clean_word}': {e}")
            corrected_words.append(word)

    result = ' '.join(corrected_words)
    print(f"DEBUG: Final result: '{result}'")
    return result

def transcribe_with_whisper(audio_file):
    """Use Whisper Python library for transcription"""
    try:
        import whisper

        # Load Whisper model (using base model for balance of speed and accuracy)
        model = whisper.load_model("base")

        # Transcribe audio
        result = model.transcribe(audio_file)
        return result["text"].strip()

    except ImportError:
        print("Whisper not available - install with: pip3 install openai-whisper")
        return None
    except Exception as e:
        print(f"Whisper error: {e}")
        return None

def transcribe_with_google(audio_file):
    """Use Google Speech Recognition"""
    try:
        recognizer = sr.Recognizer()
        
        with sr.AudioFile(audio_file) as source:
            # Adjust for ambient noise
            recognizer.adjust_for_ambient_noise(source, duration=0.5)
            audio_data = recognizer.record(source)
            
            # Try Google
            return recognizer.recognize_google(audio_data)
    except Exception as e:
        print(f"Google Speech Recognition error: {e}")
        return None

def multi_engine_transcription(audio_file):
    """Use multiple transcription engines and return all results"""
    results = []
    
    # Method 1: Whisper (if available)
    whisper_result = transcribe_with_whisper(audio_file)
    if whisper_result:
        results.append(("Whisper", whisper_result))
    
    # Method 2: Google Speech Recognition
    google_result = transcribe_with_google(audio_file)
    if google_result:
        results.append(("Google", google_result))
    
    return results

def convert_audio_to_wav(input_file):
    """Convert audio to WAV format"""
    try:
        if input_file.endswith('.wav'):
            return input_file
            
        output_file = input_file.replace(os.path.splitext(input_file)[1], "_converted.wav")
        
        # Use pydub for conversion
        audio = AudioSegment.from_file(input_file)
        audio = audio.set_channels(1)  # Convert to mono
        audio = audio.set_frame_rate(16000)  # Set to 16kHz
        audio.export(output_file, format="wav")
        
        return output_file
    except Exception as e:
        print(f"Audio conversion error: {e}")
        return None

# Check arguments
if len(sys.argv) < 3:
    print("Usage: python process_audio_hybrid.py <audio_file> <expected_text>")
    sys.exit(1)

audio_file = sys.argv[1]
expected_text = sys.argv[2]

# Load medical dictionary
medical_terms = load_medical_dictionary()

# Convert audio to WAV if needed
wav_file = convert_audio_to_wav(audio_file)
if not wav_file:
    print("Expected: " + expected_text)
    print("Transcribed: Audio conversion failed")
    print("Accuracy: 0.00")
    sys.exit(1)

# Perform multi-engine transcription
transcription_results = multi_engine_transcription(wav_file)

if not transcription_results:
    print("Expected: " + expected_text)
    print("Transcribed: Could not transcribe audio")
    print("Accuracy: 0.00")
    sys.exit(1)

# Process each result and find the best one
best_result = None
best_accuracy = 0
best_engine = None

for engine, raw_transcription in transcription_results:
    print(f"DEBUG: Processing {engine} result: '{raw_transcription}'")

    # Apply medical term correction
    corrected_transcription = fuzzy_match_medical_terms(raw_transcription, medical_terms)

    # Calculate accuracy for both raw and corrected
    raw_accuracy = get_accuracy(expected_text, raw_transcription)
    corrected_accuracy = get_accuracy(expected_text, corrected_transcription)

    print(f"DEBUG: {engine} - Raw: {raw_accuracy:.2f}%, Corrected: {corrected_accuracy:.2f}%")
    print(f"DEBUG: Raw: '{raw_transcription}' -> Corrected: '{corrected_transcription}'")

    # Choose the better result
    if corrected_accuracy > raw_accuracy and corrected_accuracy > best_accuracy:
        best_accuracy = corrected_accuracy
        best_result = corrected_transcription
        best_engine = f"{engine} (corrected)"
        print(f"DEBUG: Using corrected result from {engine}")
    elif raw_accuracy > best_accuracy:
        best_accuracy = raw_accuracy
        best_result = raw_transcription
        best_engine = engine
        print(f"DEBUG: Using raw result from {engine}")

# Print output for Node.js
print(f'Expected: {expected_text}')
print(f'Transcribed: {best_result}')
print(f'Accuracy: {best_accuracy:.2f}')

# Cleanup
if wav_file != audio_file and os.path.exists(wav_file):
    os.remove(wav_file)
