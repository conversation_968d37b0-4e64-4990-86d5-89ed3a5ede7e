#!/bin/bash

echo "Setting up enhanced speech-to-text environment..."

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "Python 3 is required but not installed. Please install Python 3 first."
    exit 1
fi

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "pip3 is required but not installed. Please install pip3 first."
    exit 1
fi

# Install Python dependencies
echo "Installing Python dependencies..."
pip3 install -r requirements.txt

# Check if ffmpeg is installed (required for audio processing)
if ! command -v ffmpeg &> /dev/null; then
    echo "Warning: ffmpeg is not installed. Please install ffmpeg for audio conversion."
    echo "On macOS: brew install ffmpeg"
    echo "On Ubuntu: sudo apt-get install ffmpeg"
    echo "On CentOS: sudo yum install ffmpeg"
fi

# Install Whisper (OpenAI's speech recognition model)
echo "Installing OpenAI Whisper..."
pip3 install openai-whisper

# Download Whisper models (optional - they will be downloaded on first use)
echo "Downloading Whisper base model (optional)..."
python3 -c "import whisper; whisper.load_model('base')" 2>/dev/null || echo "Whisper model will be downloaded on first use"

echo "Setup complete!"
echo ""
echo "To test the enhanced speech-to-text:"
echo "python3 process_audio_hybrid.py <audio_file> <expected_text>"
echo ""
echo "Available scripts:"
echo "- process_audio.py: Original + Whisper + medical term correction"
echo "- process_audio_vosk.py: Uses local Vosk models + medical term correction"
echo "- process_audio_hybrid.py: Tries multiple engines and picks the best result"
