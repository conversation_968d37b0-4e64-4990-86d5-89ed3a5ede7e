const { getDatabaseConnection } = require("../config/database");
const { Op } = require("sequelize");
const dayjs = require('dayjs');

let dailyKeywordCache = {
    date: null,
    keyword: null,
    type: null,
};

const Brands = async (req, res) => {
    const { division } = req.params;
    const { userId } = req.query;

    if (!division) {
        return res.status(400).json({ message: "Division parameter is required" });
    }
    if (!userId) {
        return res.status(400).json({ message: "UserId query parameter is required" });
    }

    try {
        const dbConnection = await getDatabaseConnection(division);
        if (!dbConnection) {
            return res.status(500).json({ message: "Database connection failed for the specified division" });
        }

        const { BrandModel, FavouriteModel } = dbConnection;

        await BrandModel.sync();
        await FavouriteModel.sync();

        // Fetch all brands
        const brandsData = await BrandModel.findAll();

        // Fetch user's favorite brands
        const favouriteBrands = await FavouriteModel.findAll({
            where: { UserId: userId, Type: "Brand" },
            attributes: ["Brand_Name"]
        });

        // Extract favorite brand names into a Set for quick lookup
        const favouriteSet = new Set(favouriteBrands.map(fav => fav.Brand_Name));

        // Add favourite flag to each brand
        const formattedBrands = brandsData.map(brand => ({
            ...brand.toJSON(),
            favourite: favouriteSet.has(brand.Brand_Name)
        }));

        res.json({
            division,
            brands: formattedBrands
        });
    } catch (error) {
        console.error("Brands API Error:", error.message);
        res.status(500).json({ message: "Error fetching data", error: error.message });
    }
};

const BrandsbyID = async (req, res) => {
    const { division, id } = req.params;
    const { userId } = req.query;

    if (!division) {
        return res.status(400).json({ message: "Division parameter is required" });
    }
    if (!userId) {
        return res.status(400).json({ message: "UserId query parameter is required" });
    }

    try {
        const dbConnection = await getDatabaseConnection(division);
        if (!dbConnection) {
            return res.status(500).json({ message: "Database connection failed for the specified division" });
        }

        const { BrandModel, FavouriteModel } = dbConnection;

        await BrandModel.sync();
        await FavouriteModel.sync();

        // Fetch all brands
        const brandsData = await BrandModel.findAll({
            where: { id: id }
        });

        // Fetch user's favorite brands
        const favouriteBrands = await FavouriteModel.findAll({
            where: { UserId: userId, Type: "Brand" },
            attributes: ["Brand_Name"]
        });

        // Extract favorite brand names into a Set for quick lookup
        const favouriteSet = new Set(favouriteBrands.map(fav => fav.Brand_Name));

        // Add favourite flag to each brand
        const formattedBrands = brandsData.map(brand => ({
            ...brand.toJSON(),
            favourite: favouriteSet.has(brand.Brand_Name)
        }));

        res.json({
            division,
            brands: formattedBrands
        });
    } catch (error) {
        console.error("Brands API Error:", error.message);
        res.status(500).json({ message: "Error fetching data", error: error.message });
    }
};

const Molecules = async (req, res) => {
    const { division } = req.params;
    const { userId } = req.query;

    if (!division) {
        return res.status(400).json({ message: "Division parameter is required" });
    }
    if (!userId) {
        return res.status(400).json({ message: "UserId query parameter is required" });
    }

    try {
        const dbConnection = await getDatabaseConnection(division);
        if (!dbConnection) {
            return res.status(500).json({ message: "Database connection failed for the specified division" });
        }

        const { MoleculeModel, FavouriteModel } = dbConnection;

        await MoleculeModel.sync();
        await FavouriteModel.sync();

        // Fetch all molecules
        const moleculesData = await MoleculeModel.findAll();

        // Fetch user's favorite molecules
        const favouriteMolecules = await FavouriteModel.findAll({
            where: { UserId: userId, Type: "Molecule" },
            attributes: ["Molecule_Name"]
        });

        // Extract favorite molecule names into a Set for quick lookup
        const favouriteSet = new Set(favouriteMolecules.map(fav => fav.Molecule_Name));

        // Add favourite flag to each molecule
        const formattedMolecules = moleculesData.map(molecule => ({
            ...molecule.toJSON(),
            favourite: favouriteSet.has(molecule.Molecule_Name)
        }));

        res.json({
            division,
            molecules: formattedMolecules
        });
    } catch (error) {
        console.error("Molecules API Error:", error.message);
        res.status(500).json({ message: "Error fetching data", error: error.message });
    }
};

const MoleculesbyId = async (req, res) => {
    const { division, id } = req.params;
    const { userId } = req.query;

    if (!division) {
        return res.status(400).json({ message: "Division parameter is required" });
    }
    if (!userId) {
        return res.status(400).json({ message: "UserId query parameter is required" });
    }

    try {
        const dbConnection = await getDatabaseConnection(division);
        if (!dbConnection) {
            return res.status(500).json({ message: "Database connection failed for the specified division" });
        }

        const { MoleculeModel, FavouriteModel } = dbConnection;

        await MoleculeModel.sync();
        await FavouriteModel.sync();

        // Fetch all molecules
        const moleculesData = await MoleculeModel.findAll({
            where: { id: id }
        });

        // Fetch user's favorite molecules
        const favouriteMolecules = await FavouriteModel.findAll({
            where: { UserId: userId, Type: "Molecule" },
            attributes: ["Molecule_Name"]
        });

        // Extract favorite molecule names into a Set for quick lookup
        const favouriteSet = new Set(favouriteMolecules.map(fav => fav.Molecule_Name));

        // Add favourite flag to each molecule
        const formattedMolecules = moleculesData.map(molecule => ({
            ...molecule.toJSON(),
            favourite: favouriteSet.has(molecule.Molecule_Name)
        }));

        res.json({
            division,
            molecules: formattedMolecules
        });
    } catch (error) {
        console.error("Molecules API Error:", error.message);
        res.status(500).json({ message: "Error fetching data", error: error.message });
    }
};

const Search = async (req, res) => {
    const { division } = req.params;
    const { query, userId } = req.query;

    if (!division) {
        return res.status(400).json({ message: "Division parameter is required" });
    }
    if (!userId) {
        return res.status(400).json({ message: "UserId query parameter is required" });
    }
    if (!query) {
        return res.status(400).json({ message: "Search query parameter is required" });
    }

    try {
        const dbConnection = await getDatabaseConnection(division);
        if (!dbConnection) {
            return res.status(500).json({ message: "Database connection failed for the specified division" });
        }

        const { BrandModel, MoleculeModel, FavouriteModel } = dbConnection;

        await BrandModel.sync();
        await MoleculeModel.sync();
        await FavouriteModel.sync();

        // Search in brands
        const brandsData = await BrandModel.findAll({
            where: {
                [Op.or]: [
                    { Brand: { [Op.like]: `%${query}%` } },
                    { Brand_Name: { [Op.like]: `%${query}%` } },
                    { Definition: { [Op.like]: `%${query}%` } },
                    { Vocab_Breakdown: { [Op.like]: `%${query}%` } },
                    { Mode_of_Action: { [Op.like]: `%${query}%` } },
                    { Common_Usage: { [Op.like]: `%${query}%` } }
                ]
            }
        });

        // Search in molecules
        const moleculesData = await MoleculeModel.findAll({
            where: {
                [Op.or]: [
                    { Molecule_Name: { [Op.like]: `%${query}%` } },
                    { Associated_Brands: { [Op.like]: `%${query}%` } },
                    { Definition: { [Op.like]: `%${query}%` } },
                    { Vocab_Breakdown: { [Op.like]: `%${query}%` } },
                    { Mode_of_Action: { [Op.like]: `%${query}%` } },
                    { Common_Usage: { [Op.like]: `%${query}%` } }
                ]
            }
        });

        // Fetch user's favorites
        const favouriteBrands = await FavouriteModel.findAll({
            where: { UserId: userId, Type: "Brand" },
            attributes: ["Brand_Name"]
        });

        const favouriteMolecules = await FavouriteModel.findAll({
            where: { UserId: userId, Type: "Molecule" },
            attributes: ["Molecule_Name"]
        });

        // Extract favorite names into Sets for quick lookup
        const favouriteBrandSet = new Set(favouriteBrands.map(fav => fav.Brand_Name));
        const favouriteMoleculeSet = new Set(favouriteMolecules.map(fav => fav.Molecule_Name));

        // Format results
        const formattedBrands = brandsData.map(brand => ({
            ...brand.toJSON(),
            favourite: favouriteBrandSet.has(brand.Brand_Name)
        }));

        const formattedMolecules = moleculesData.map(molecule => ({
            ...molecule.toJSON(),
            favourite: favouriteMoleculeSet.has(molecule.Molecule_Name)
        }));

        res.json({
            division,
            results: {
                brands: formattedBrands,
                molecules: formattedMolecules
            }
        });
    } catch (error) {
        console.error("Search API Error:", error.message);
        res.status(500).json({ message: "Error searching data", error: error.message });
    }
};

const KeywordSearch = async (req, res) => {
    const { division } = req.params;
    const { text } = req.body;

    if (!division) {
        return res.status(400).json({ message: "Division parameter is required" });
    }
    if (!text) {
        return res.status(400).json({ message: "Search text is required in the request body" });
    }

    try {
        const dbConnection = await getDatabaseConnection(division);
        if (!dbConnection) {
            return res.status(500).json({ message: "Database connection failed for the specified division" });
        }

        const { BrandModel, MoleculeModel } = dbConnection;

        await BrandModel.sync();
        await MoleculeModel.sync();

        // Search in brands
        const brandsData = await BrandModel.findAll({
            where: {
                [Op.or]: [
                    { Brand: { [Op.like]: `%${text}%` } },
                    { Brand_Name: { [Op.like]: `%${text}%` } },
                    { Definition: { [Op.like]: `%${text}%` } },
                    { Vocab_Breakdown: { [Op.like]: `%${text}%` } },
                    { Mode_of_Action: { [Op.like]: `%${text}%` } },
                    { Common_Usage: { [Op.like]: `%${text}%` } }
                ]
            }
        });

        // Search in molecules
        const moleculesData = await MoleculeModel.findAll({
            where: {
                [Op.or]: [
                    { Molecule_Name: { [Op.like]: `%${text}%` } },
                    { Associated_Brands: { [Op.like]: `%${text}%` } },
                    { Definition: { [Op.like]: `%${text}%` } },
                    { Vocab_Breakdown: { [Op.like]: `%${text}%` } },
                    { Mode_of_Action: { [Op.like]: `%${text}%` } },
                    { Common_Usage: { [Op.like]: `%${text}%` } }
                ]
            }
        });

        // Format results in the requested format
        const results = [];

        // Add brand results
        brandsData.forEach(brand => {
            const brandData = brand.toJSON();
            results.push({
                brand: brandData.Brand_Name || brandData.Brand,
                molecule: ""
            });
        });

        // Add molecule results
        moleculesData.forEach(molecule => {
            const moleculeData = molecule.toJSON();
            results.push({
                brand: "",
                molecule: moleculeData.Molecule_Name
            });
        });

        res.json({
            division,
            results: results
        });
    } catch (error) {
        console.error("Keyword Search API Error:", error.message);
        res.status(500).json({ message: "Error searching data", error: error.message });
    }
};

const RandomKeyword = async (req, res) => {
    const { division } = req.params;

    if (!division) {
        return res.status(400).json({ message: "Division parameter is required" });
    }

    const dbConnection = await getDatabaseConnection(division);
    if (!dbConnection) {
        return res.status(500).json({ message: "Database connection failed for the specified division" });
    }

    const { MoleculeModel, BrandModel } = dbConnection;
    const today = dayjs().format("YYYY-MM-DD");

    // ✅ Return cached keyword if already picked for today
    if (dailyKeywordCache.date === today) {
        return res.json(dailyKeywordCache.keyword);
    }

    try {
        // ✅ Fetch full fields for Molecules and Brands
        const [molecules, brands] = await Promise.all([
            MoleculeModel.findAll({}),
            BrandModel.findAll({})
        ]);

        // ✅ Combine and format
        const dataPool = [
            ...molecules.map(m => ({
                type: "Molecule",
                data: {
                    Molecule_Name: m.Molecule_Name,
                    Associated_Brands: m.Associated_Brands,
                    Vocab_Breakdown: m.Vocab_Breakdown,
                    Definition: m.Definition,
                    Mode_of_Action: m.Mode_of_Action,
                    Common_Usage: m.Common_Usage
                }
            })),
            ...brands.map(b => ({
                type: "Brand",
                data: {
                    Brand: b.Brand,
                    Brand_Name: b.Brand_Name,
                    Vocab_Breakdown: b.Vocab_Breakdown,
                    Definition: b.Definition,
                    Mode_of_Action: b.Mode_of_Action,
                    Common_Usage: b.Common_Usage
                }
            }))
        ];

        if (dataPool.length === 0) {
            return res.status(404).json({ message: "No data available" });
        }

        // ✅ Pick one random item
        const randomIndex = Math.floor(Math.random() * dataPool.length);
        const selectedKeyword = dataPool[randomIndex];

        // ✅ Save in cache
        dailyKeywordCache = {
            date: today,
            keyword: selectedKeyword,
            type: selectedKeyword.type
        };

        return res.json(selectedKeyword);

    } catch (error) {
        return res.status(500).json({ message: "Error fetching data", error });
    }
}

const GetUserProgress = async (req, res) => {
    const { division, id } = req.params;

    if (!division || !id) {
        return res.status(400).json({ message: "Division and ID parameter is required" });
    }

    const dbConnection = await getDatabaseConnection(division);
    if (!dbConnection) {
        return res.status(500).json({ message: "Database connection failed for the specified division" });
    }

    const { MoleculeModel, BrandModel, UserWordModel } = dbConnection;

    try {
        // ✅ Fetch all words
        const [molecules, brands] = await Promise.all([
            MoleculeModel.findAll({}),
            BrandModel.findAll({})
        ]);

        const totalLength = molecules.length + brands.length;

        // ✅ Fetch all user's completed words
        const completedWords = await UserWordModel.findAll({
            where: { user_id: id },
            attributes: ['word', 'type', 'brand_id','molecule_id']  // assuming 'word', 'type', 'brand_id' and'molecule_id' exist
        });

        const userCompleted = completedWords.length;

        // ✅ Filter by type
        const completedBrands = completedWords.filter(word => word.type === 'Brand');
        const completedMolecules = completedWords.filter(word => word.type === 'Molecule');

        // ✅ Calculate percentage
        const completionPercentage = totalLength > 0
            ? ((userCompleted / totalLength) * 100).toFixed(2)
            : 0;

        res.json({
            totalWords: totalLength,
            userCompleted,
            completionPercentage: `${completionPercentage}%`,
            completedBrands: completedBrands.map(item => ({
                id: item.brand_id,
                name: item.word
            })),
            completedMolecules: completedMolecules.map(item => ({
                id: item.molecule_id,
                name: item.word
            }))
        });

    } catch (error) {
        return res.status(500).json({ message: "Error fetching progress", error });
    }

};

const AddUserProgress = async (req, res) => {
    const { division } = req.params;
    const { word, user_id, type, brand_id, molecule_id } = req.body;

    // Basic validations
    if (!division) {
        return res.status(400).json({ message: "Division parameter is required" });
    }

    if (!word || !user_id || !type) {
        return res.status(400).json({ message: "word, user_id, and type are required" });
    }

    // Conditional validation based on type
    if (type === 'Molecule' && !molecule_id) {
        return res.status(400).json({ message: "molecule_id is required when type is Molecule" });
    }

    if (type === 'Brand' && !brand_id) {
        return res.status(400).json({ message: "brand_id is required when type is Brand" });
    }

    // Database connection
    const dbConnection = await getDatabaseConnection(division);
    if (!dbConnection) {
        return res.status(500).json({ message: "Database connection failed for the specified division" });
    }

    const { UserWordModel } = dbConnection;

    try {
        // Check for existing entry
        const existingEntry = await UserWordModel.findOne({
            where: { word, user_id }
        });

        if (existingEntry) {
            return res.status(200).json({
                message: "User has already completed this word.",
                condition: false
            });
        }

        // Prepare dynamic insert payload
        const newEntry = {
            word,
            user_id,
            type,
            brand_id: brand_id || null,
            molecule_id: molecule_id || null
        };

        // Insert data
        const newProgress = await UserWordModel.create(newEntry);
        return res.status(201).json(newProgress);

    } catch (error) {
        return res.status(500).json({ message: "Error saving progress", error });
    }
};

module.exports = { AddUserProgress };



module.exports = {
    Brands,
    BrandsbyID,
    Molecules,
    MoleculesbyId,
    Search,
    KeywordSearch,
    RandomKeyword,
    GetUserProgress,
    AddUserProgress
};
