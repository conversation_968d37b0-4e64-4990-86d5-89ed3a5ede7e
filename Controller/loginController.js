const { Op } = require('sequelize');
const jwt = require("jsonwebtoken");
require('dotenv').config();

const { getDatabaseConnection } = require("../config/database");
const JWT_SECRET = process.env.JWT_SECRET || "digilabs@2026medical";

const Login = async (req, res) => {
    const { division } = req.params;
    const { sapcode, password } = req.body;

    if (!division) {
        return res.status(400).json({ message: "Division parameter is required" });
    }

    if (!sapcode || !password) {
        return res.status(400).json({ message: "sapcode (or email) and password are required" });
    }

    try {
        const { LoginModel } = await getDatabaseConnection(division);

        // Search by sap or email
        const user = await LoginModel.findOne({
            where: {
                [Op.or]: [
                    { sap: sapcode },
                    { emailId: sapcode }  // allow login using email as well
                ]
            }
        });

        if (!user) {
            return res.status(404).json({ message: "User not found" });
        }

        if (password !== "eyecare") {
            return res.status(401).json({ message: "Invalid password" });
        }

        const token = jwt.sign(
            {
                id: user.id,
                name: user.name,
                sap: user.sap,
                division
            },
            JWT_SECRET,
            { expiresIn: "7d" }
        );

        return res.status(200).json({
            message: "Login successful",
            token,
            user
        });

    } catch (error) {
        console.error("Login Error:", error);
        return res.status(500).json({ message: "Internal server error" });
    }
};

module.exports = {
    Login
};
