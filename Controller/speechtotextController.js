const { exec } = require("child_process");
const path = require("path");
const fs = require("fs");

const speechtoTextPython = async (req, res) => {
    try {
        if (!req.file || !req.body.expectedWord) {
            return res.status(400).json({ error: "No file uploaded or expected word missing" });
        }

        const originalFilePath = path.join(__dirname, "../uploads", req.file.filename);
        const convertedFilePath = originalFilePath.replace(".wav", "_converted.wav");
        const pythonScriptPath = path.join(__dirname, "../services/PythonService/process_audio.py");

        const expectedWord = req.body.expectedWord.trim();

        // Convert WAV file to PCM 16-bit WAV format using FFmpeg
        exec(`ffmpeg -i "${originalFilePath}" -ar 16000 -ac 1 -sample_fmt s16 "${convertedFilePath}" -y`, (ffmpegError) => {
            if (ffmpegError) {
                console.error("FFmpeg Error:", ffmpegError);
                return res.status(500).json({ error: "Audio conversion failed" });
            }

            console.log("Audio converted successfully:", convertedFilePath);

            // Now, pass the converted file to Python script
            exec(`python3 "${pythonScriptPath}" "${convertedFilePath}" "${expectedWord}"`, (error, stdout, stderr) => {
                if (error) {
                    console.error("Python Script Error:", error);
                    return res.status(500).json({ error: "Python script execution failed" });
                }
                if (stderr) {
                    console.error("Python Stderr:", stderr);
                }

                console.log("Python Output:", stdout);

                // Extract values from stdout
                const outputLines = stdout.trim().split("\n");
                if (outputLines.length < 3) {
                    return res.status(500).json({ error: "Unexpected Python output" });
                }

                const expectedText = outputLines[0].replace("Expected: ", "").trim();
                const transcribedText = outputLines[1].replace("Transcribed: ", "").trim();
                const accuracy = parseFloat(outputLines[2].replace("Accuracy: ", "").trim());

                res.json({
                    expectedText,
                    transcribedText,
                    accuracy: `${accuracy.toFixed(2)}`
                });

                // Cleanup old files
                fs.unlink(originalFilePath, (err) => {
                    if (err) console.error("Error deleting original file:", err);
                });

                fs.unlink(convertedFilePath, (err) => {
                    if (err) console.error("Error deleting converted file:", err);
                });
            });
        });

    } catch (error) {
        console.error("Webhook Error:", error);
        res.status(500).json({ error: "Internal Server Error" });
    }
};

module.exports = { speechtoTextPython };
