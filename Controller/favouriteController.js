const { getDatabaseConnection } = require('../config/database');

const favAdd = async (req, res) => {
    const { division } = req.params;
    const {
        action, // 'add' or 'delete'
        UserId,
        Type,
        Name,
        Vocab_Breakdown,
        Definition,
        Mode_of_Action,
        Common_Usage,
        Brand_Name,
        Molecule_Name,
        Associated_Brands
    } = req.body;

    try {
        const { FavouriteModel } = await getDatabaseConnection(division);

        if (!UserId || !Type || !action) {
            return res.status(400).json({ message: 'UserId, Type, and action are required' });
        }

        const whereCondition = { UserId };
        if (Type === 'Brand') {
            if (!Brand_Name) {
                return res.status(400).json({ message: 'Brand_Name is required for Brand type' });
            }
            whereCondition.Brand_Name = Brand_Name;
        } else if (Type === 'Molecule') {
            if (!Molecule_Name) {
                return res.status(400).json({ message: 'Molecule_Name is required for Molecule type' });
            }
            whereCondition.Molecule_Name = Molecule_Name;
        }

        // 🗑️ DELETE logic
        if (action === 'delete') {
            const deleted = await FavouriteModel.destroy({ where: whereCondition });
            if (deleted) {
                return res.status(200).json({ message: 'Favourite deleted successfully' });
            } else {
                return res.status(404).json({ message: 'Favourite not found' });
            }
        }

        // ✅ ADD logic
        const isExist = await FavouriteModel.findOne({ where: whereCondition });
        if (isExist) {
            return res.status(409).json({ message: 'This is already added into favourites' });
        }

        const favouriteData = {
            UserId,
            Type,
            Vocab_Breakdown,
            Definition,
            Mode_of_Action,
            Common_Usage
        };

        if (Type === 'Brand') {
            favouriteData.Name = Name;
            favouriteData.Brand_Name = Brand_Name;
        } else if (Type === 'Molecule') {
            favouriteData.Molecule_Name = Molecule_Name;
            favouriteData.Associated_Brands = Associated_Brands;
        }

        const favourite = await FavouriteModel.create(favouriteData);

        return res.status(201).json({
            message: 'Favourite added successfully',
            data: favourite
        });

    } catch (error) {
        console.error('Error in favourite operation:', error);
        return res.status(500).json({ message: 'Internal Server Error', error: error.message });
    }
};


const favfetch = async (req, res) => {
    const { division, userId } = req.params;
    const { type } = req.query;
    
    try {
        const { FavouriteModel } = await getDatabaseConnection(division);

        if (!userId) {
            return res.status(400).json({ message: 'UserId is required' });
        }

        // Build the query condition
        const whereCondition = { UserId: userId };
        if (type === 'brands') {
            whereCondition.Type = 'Brand';
        } else if (type === 'molecules') {
            whereCondition.Type = 'Molecule';
        }
        console.log(whereCondition);
        // Fetch data
        const favourites = await FavouriteModel.findAll({ where: whereCondition });

        // Return response
        return res.status(200).json({
            message: 'Favourites fetched successfully',
            total: favourites.length,
            data: favourites
        });

    } catch (error) {
        console.error('Error fetching favourites:', error);
        return res.status(500).json({ message: 'Internal Server Error', error: error.message });
    }
};

module.exports = {
    favAdd,
    favfetch
};
