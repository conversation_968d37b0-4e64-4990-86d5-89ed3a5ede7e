const {
    detectIntent,
    updateEntity
} = require('../services/dialogflowservice');
const uuid = require('uuid');

const textModification = async (req, res) => {
    try {
        // const { division } = req.params;
        const { userEntity, entityName, entity, userID } = req.body;

        console.log({ userEntity, entityName, entity, userID });

        let newEntity = entity.split("_")[0].toLowerCase();
        let newEntityName = entityName.replace(newEntity, '').trim();

        console.log({ newEntity, newEntityName });

        if (!userEntity || !entityName || !entity || !userID) {
            return res.status(400).json({ error: "Missing required fields." });
        }

        const sessionId = `${uuid.v4()}-${userID}`;

        await updateEntity(userEntity, newEntityName, entity, sessionId);

        let entitysplit = entity.toLowerCase().split('_')[0];

        const result = await detectIntent(entitysplit, userEntity, sessionId);

        res.json({ reply: result });

    } catch (error) {
        console.error('Webhook Error:', error);
        res.status(500).json({ error: 'Internal Server Error' });
    }
}

module.exports = {
    textModification
}