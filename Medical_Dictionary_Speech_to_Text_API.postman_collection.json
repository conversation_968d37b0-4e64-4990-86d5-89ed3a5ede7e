{"info": {"_postman_id": "12345678-1234-1234-1234-123456789abc", "name": "Medical Dictionary - Enhanced Speech-to-Text API", "description": "Test collection for the enhanced speech-to-text system with medical term correction capabilities. Tests medical terms like Brinzolamide, resync, Travisight, and Veldrop.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Speech to Text - Hybrid Method (Recommended)", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "audio", "type": "file", "src": "/path/to/your/audio/file.wav", "description": "Upload your audio file (WAV, MP3 supported)"}, {"key": "expectedWord", "value": "Brinzolamide", "type": "text", "description": "The medical term you expect to be recognized"}]}, "url": {"raw": "{{base_url}}/eyecare/speechtotext?method=hybrid", "host": ["{{base_url}}"], "path": ["eyecare", "speechtotext"], "query": [{"key": "method", "value": "hybrid", "description": "Uses multiple engines and picks best result"}]}, "description": "Test speech-to-text with hybrid method (recommended). Uses multiple recognition engines and medical term correction."}, "response": []}, {"name": "Speech to Text - Whisper Method", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "audio", "type": "file", "src": "/path/to/your/audio/file.wav", "description": "Upload your audio file (WAV, MP3 supported)"}, {"key": "expectedWord", "value": "Travis<PERSON>", "type": "text", "description": "The medical term you expect to be recognized"}]}, "url": {"raw": "{{base_url}}/eyecare/speechtotext?method=whisper", "host": ["{{base_url}}"], "path": ["eyecare", "speechtotext"], "query": [{"key": "method", "value": "whisper", "description": "Uses OpenAI Whisper for highest accuracy"}]}, "description": "Test speech-to-text with <PERSON>his<PERSON> method. Uses OpenAI's state-of-the-art speech recognition model."}, "response": []}, {"name": "Speech to Text - <PERSON>osk Method (Offline)", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "audio", "type": "file", "src": "/path/to/your/audio/file.wav", "description": "Upload your audio file (WAV, MP3 supported)"}, {"key": "expectedWord", "value": "Veldrop", "type": "text", "description": "The medical term you expect to be recognized"}]}, "url": {"raw": "{{base_url}}/eyecare/speechtotext?method=vosk", "host": ["{{base_url}}"], "path": ["eyecare", "speechtotext"], "query": [{"key": "method", "value": "vosk", "description": "Uses offline Vosk models"}]}, "description": "Test speech-to-text with Vosk method. Uses offline speech recognition models."}, "response": []}, {"name": "Test - Brinzolamide", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "audio", "type": "file", "src": "/path/to/brinzolamide_audio.wav", "description": "Audio file saying 'Brinzolamide'"}, {"key": "expectedWord", "value": "Brinzolamide", "type": "text"}]}, "url": {"raw": "{{base_url}}/eyecare/speechtotext?method=hybrid", "host": ["{{base_url}}"], "path": ["eyecare", "speechtotext"], "query": [{"key": "method", "value": "hybrid"}]}, "description": "Test the problematic medical term 'Brinzolamide' that was previously transcribed incorrectly."}, "response": []}, {"name": "Test - resync", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "audio", "type": "file", "src": "/path/to/resync_audio.wav", "description": "Audio file saying 'resync'"}, {"key": "expectedWord", "value": "resync", "type": "text"}]}, "url": {"raw": "{{base_url}}/eyecare/speechtotext?method=hybrid", "host": ["{{base_url}}"], "path": ["eyecare", "speechtotext"], "query": [{"key": "method", "value": "hybrid"}]}, "description": "Test medical device synchronization term 'resync'."}, "response": []}, {"name": "Test - <PERSON><PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "audio", "type": "file", "src": "/path/to/travisight_audio.wav", "description": "Audio file saying '<PERSON><PERSON>'"}, {"key": "expectedWord", "value": "Travis<PERSON>", "type": "text"}]}, "url": {"raw": "{{base_url}}/eyecare/speechtotext?method=hybrid", "host": ["{{base_url}}"], "path": ["eyecare", "speechtotext"], "query": [{"key": "method", "value": "hybrid"}]}, "description": "Test medical imaging/vision system term 'Travisight'."}, "response": []}, {"name": "Test - Veldrop", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "audio", "type": "file", "src": "/path/to/veldrop_audio.wav", "description": "Audio file saying 'Veldrop'"}, {"key": "expectedWord", "value": "Veldrop", "type": "text"}]}, "url": {"raw": "{{base_url}}/eyecare/speechtotext?method=hybrid", "host": ["{{base_url}}"], "path": ["eyecare", "speechtotext"], "query": [{"key": "method", "value": "hybrid"}]}, "description": "Test medical eye drop/device term 'Veldrop'."}, "response": []}, {"name": "Test - Other Medical Terms", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "audio", "type": "file", "src": "/path/to/medical_term_audio.wav", "description": "Audio file with medical term"}, {"key": "expectedWord", "value": "Dorzolamide", "type": "text", "description": "Try: Dorzolamide, Timolol, Latanoprost, Glaucoma, etc."}]}, "url": {"raw": "{{base_url}}/eyecare/speechtotext?method=hybrid", "host": ["{{base_url}}"], "path": ["eyecare", "speechtotext"], "query": [{"key": "method", "value": "hybrid"}]}, "description": "Test other medical terms from the dictionary. Change expected<PERSON><PERSON> to test different terms."}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:3000", "description": "Base URL for your API server"}]}