const jwt = require('jsonwebtoken');
require('dotenv').config();
// Use environment variable or default fallback
const JWT_SECRET = process.env.JWT_SECRET || "digilabs@2026medical";

const authenticateToken = (req, res, next) => {
    // Expected format: Authorization: Bearer <token>
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ message: 'Access denied. Token missing.' });
    }

    try {
        const decoded = jwt.verify(token, JWT_SECRET);
        req.user = decoded; // Attach decoded user info to request
        next();
    } catch (err) {
        return res.status(403).json({ message: 'Invalid or expired token.' });
    }
};

module.exports = authenticateToken;
